---
import ListItemAgenda from '../common/ListItemAgenda.astro';

interface Props {
  events: any;
  pathPrefix: string;
}

const { events, pathPrefix } = Astro.props;

// Ordina gli eventi per day_of_the_week (1-7, Lun-<PERSON>)
const sortedEvents = events?.sort((a, b) => a.day_of_the_week - b.day_of_the_week);

---

<ul class="divide-y divide-gray-200 dark:divide-gray-700 space-y-1">
  {sortedEvents?.map((event) => <ListItemAgenda pathPrefix={pathPrefix} event={event} />)}
</ul>

<style>
  .list-item:hover {
    @apply bg-gray-200 dark:bg-gray-700;
    cursor: pointer;
  }
</style>
