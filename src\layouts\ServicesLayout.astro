---
import Layout from '~/layouts/PageLayout.astro';
import { services } from '~/connectors/index';
import { getLangFromUrl, useTranslations } from '~/i18n/utils';
import ServiceHorizontalComponent from '~/components/services/ServiceHorizontalComponent.astro';
import HeroServices from '~/components/services/HeroServices.astro';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

const metadata = {
  title: t('servicesPage.metadata.title'),
  description: t('servicesPage.metadata.description'),
};
---

<Layout metadata={metadata} noDonate>
  <HeroServices />

  <section class="flex flex-col justify-center antialiased my-8">
    {
      services.map((s) => (
        <div class="my-4">
          <ServiceHorizontalComponent
            name={t(`services.${s.name_t}`)}
            cover={s.cover}
            emoji={s.emoji}
            shortDescription={t(`services.${s.name_t}.description`)}
            slug={t(`services.${s.name_t}.slug`)}
          />
        </div>
      ))
    }
  </section>
</Layout>
