---
import WidgetWrapper from '~/components/ui/WidgetWrapper.astro';
import Button from '~/components/ui/Button.astro';
import Item from '~/components/common/GridItemEvent.astro';
import type { Widget } from '~/types';
import { latinEvents, socialEvents } from '~/connectors/index';

interface Props extends Widget {
  title?: string;
  linkText?: string;
  linkUrl?: string | URL;
  information?: string;
  count?: number;
  officials?: boolean;
  addScript?: boolean;
  flyersPreview?: boolean;
  eventType?: 'latin' | 'social';
}

const {
  id,
  isDark = false,
  classes = {},
  bg = await Astro.slots.render('bg'),
  title = await Astro.slots.render('title'),
  linkText,
  linkUrl,
  information = await Astro.slots.render('information'),
  officials = false,
  addScript = false,
  flyersPreview = false,
  eventType = 'latin',
} = Astro.props;

const events = eventType === 'latin' ? latinEvents : socialEvents;
const filteredEvents = officials ? events.filter((e) => e.flyer) : events;

const BREAKPOINTS = {
  mobile: { width: 320, slides: 1, spacing: 20 },
  tablet: { width: 480, slides: 3, spacing: 30 },
  desktop: { width: 640, slides: 4, spacing: 40 },
};

const pathPrefix = eventType === 'latin' ? 'dancing/agenda' : 'socialization/agenda';
---

<WidgetWrapper id={id} isDark={isDark} containerClass={classes?.container} bg={bg}>
  <header class="flex flex-col lg:flex-row lg:justify-between mb-8">
    {
      title && (
        <div class="md:max-w-sm">
          <h2
            class="text-3xl font-bold tracking-tight sm:text-4xl sm:leading-none group font-heading mb-2"
            set:html={title}
          />
          {linkText && linkUrl && (
            <Button variant="link" href={linkUrl}>
              {linkText}
            </Button>
          )}
        </div>
      )
    }
    {information && <p class="text-muted dark:text-slate-400 lg:text-sm lg:max-w-md" set:html={information} />}
  </header>

  <div id="events-slider" class="swiper">
    <div class="swiper-wrapper py-6">
      {
        filteredEvents.map((event) => (
          <div class="swiper-slide">
            <Item pathPrefix={pathPrefix} event={event} flyersPreview={flyersPreview} />
          </div>
        ))
      }
    </div>

    <button id="slider-button-left" class="swiper-button-prev slider-button" aria-label="Previous slide">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
        <path
          d="M10.0002 11.9999L6 7.99971L10.0025 3.99719"
          stroke="currentColor"
          stroke-width="1.6"
          stroke-linecap="round"
          stroke-linejoin="round"></path>
      </svg>
    </button>
    <button id="slider-button-right" class="swiper-button-next slider-button" aria-label="Next slide">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
        <path
          d="M5.99984 4.00012L10 8.00029L5.99748 12.0028"
          stroke="currentColor"
          stroke-width="1.6"
          stroke-linecap="round"
          stroke-linejoin="round"></path>
      </svg>
    </button>
  </div>
</WidgetWrapper>

{
  addScript && (
    <>
      <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
      <script is:inline src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js" />
    </>
  )
}

<style>
  .swiper-button-prev::after,
  .swiper-button-next::after {
    content: none;
  }

  .slider-button {
    @apply bg-black/50 p-2 flex justify-center items-center w-12 h-12 transition-all duration-300 rounded-full top-2/4 -translate-y-8 outline-none;
    @apply focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-offset-white focus-visible:ring-black dark:focus-visible:ring-offset-black dark:focus-visible:ring-white;
    @apply active:bg-black/70;
    -webkit-tap-highlight-color: transparent;
  }

  .slider-button svg {
    @apply h-5 w-5 text-white transition-transform duration-300;
  }

  #slider-button-left {
    @apply left-5;
  }

  #slider-button-right {
    @apply right-5;
  }

  /* Apply hover effects only on devices that support hover */
  @media (hover: hover) {
    .slider-button {
      @apply hover:bg-black/70 hover:w-14 hover:h-14;
    }

    .slider-button:hover svg {
      @apply transform scale-110;
    }
  }

  /* Styles for devices that don't support hover (like most touchscreens) */
  @media (hover: none) {
    .slider-button:active {
      @apply bg-black/70;
    }
  }
</style>

<script define:vars={{ BREAKPOINTS }}>
  const initializeSwiper = () => {
    const swiper = new Swiper('#events-slider', {
      preventClicks: true,
      autoHeight: true,
      slidesPerView: 'auto',
      spaceBetween: 4,
      direction: 'horizontal',
      loop: true,
      breakpoints: Object.fromEntries(
        Object.entries(BREAKPOINTS).map(([, value]) => [
          value.width,
          {
            slidesPerView: value.slides,
            spaceBetween: value.spacing,
          },
        ])
      ),
      navigation: {
        nextEl: '.swiper-button-next',
        prevEl: '.swiper-button-prev',
      },
      keyboard: {
        enabled: true,
        onlyInViewport: true,
      },
    });

    const currentDay = new Date().getDay();
    swiper.slideTo(currentDay - 1);

    // Add keyboard navigation
    document.addEventListener('keydown', (e) => {
      if (e.key === 'ArrowLeft') {
        swiper.slidePrev();
      } else if (e.key === 'ArrowRight') {
        swiper.slideNext();
      }
    });
  };

  // Initialize swiper when the script loads
  initializeSwiper();
</script>
