---
// import type { ImageMetadata } from 'astro';
import Layout from '~/layouts/PageLayout.astro';
import { services, vocabulary, reviews } from '~/connectors/index';
// import PinesAlbum from '~/components/PinesAlbum.astro';
import CustomFaqs from '~/components/common/CustomFaqs.astro';
import Portfolio from '~/components/common/Portfolio.astro';
import Testimonials from '~/components/widgets/Testimonials.astro';
import Hero from '~/components/widgets/Hero.astro';
import Features2 from '~/components/widgets/Features2.astro';
import ServiceWidget from '~/components/services/ServiceWidget.astro';

import { getLangFromUrl, useTranslations, useTranslatedPath } from '~/i18n/utils';
import Pricing from '~/components/widgets/Pricing.astro';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);

const KEY = 'photographer';

const service = services.find((s) => s.name_t === KEY);
const faqs = vocabulary.filter((item) => item.key.startsWith(`faqs.${KEY}`));

const prices = [
  {
    title: 'standard',
    subtitle: `Shooting time: approx. 90 min.
Location: 1 Place, Gran Canaria
A one-time change of clothes or looks, if you wish, is included in the price
100 + high-quality, full-resolution images selected by the photographer easy editing
3 work days delivery. Each additional place +100 €

Depending on the place for the photo shoot, a maximum of 2 in the morning and 2 in the evening Photo shoot time in multiple locations: 60 min. each place

NEW! Short video +100 € /all places`,
    price: '200',
    period: '/ one time',
    callToAction: {
      targetBlank: true,
      text: "Let's go!",
      href: '#',
    },
  },
  {
    title: 'gold',
    subtitle: 'Premium',
    price: 250,
    period: '/ one time',
    callToAction: {
      targetBlank: true,
      text: "Let's go!",
      href: '#',
    },
    hasRibbon: true,
    ribbonTitle: 'popular',
  },
  {
    title: 'Platinum',
    subtitle: 'Tailored solutions',
    price: 300,
    period: '/ one time',
    callToAction: {
      targetBlank: true,
      text: "Let's go!",
      href: '#',
    },
  },
];

let temp = {};
faqs.forEach((faq) => {
  let number = faq.key.split('.').pop();
  if (!temp[number]) temp[number] = {};
  if (faq.key.includes('question')) {
    temp[number].question = faq.key;
  } else if (faq.key.includes('answer')) {
    temp[number].answer = faq.key;
  }
});

let faqsComputed = Object.values(temp);

const galleryComputed = [
  {
    src: '/images/services/photographer/gallery/mountains-01.jpeg',
    alt: 'Mountains 01',
  },
  {
    src: '/images/services/photographer/gallery/mountains-02.jpeg',
    alt: 'Mountains 02',
  },
  {
    src: '/images/services/photographer/gallery/mountains-03.jpeg',
    alt: 'Mountains 03',
  },
  {
    src: '/images/services/photographer/gallery/mountains-04.jpeg',
    alt: 'Mountains 04',
  },
  {
    src: '/images/services/photographer/gallery/mountains-05.jpeg',
    alt: 'Mountains 05',
  },
  {
    src: '/images/services/photographer/gallery/mountains-06.jpeg',
    alt: 'Mountains 06',
  },
  {
    src: '/images/services/photographer/gallery/mountains-07.jpeg',
    alt: 'Mountains 07',
  },
  {
    src: '/images/services/photographer/gallery/mountains-08.jpeg',
    alt: 'Mountains 08',
  },
  {
    src: '/images/services/photographer/gallery/mountains-09.jpeg',
    alt: 'Mountains 09',
  },
  {
    src: '/images/services/photographer/gallery/mountains-10.jpeg',
    alt: 'Mountains 10',
  },
];

const portfolioComputed = [
  {
    src: '/images/services/photographer/portfolio/interior-photographer.jpg',
    alt: 'Interior Photographer',
  },
  {
    src: '/images/services/photographer/portfolio/love-story-couples.jpg',
    alt: 'Love Story Couples',
  },
  {
    src: '/images/services/photographer/portfolio/portrait-photos.jpg',
    alt: 'Portrait Photos',
  },
  {
    src: '/images/services/photographer/portfolio/pregnancy-photo-shoot.jpg',
    alt: 'Pregnancy Photo Shoot',
  },
  {
    src: '/images/services/photographer/portfolio/sports-photo.jpg',
    alt: 'Sports Photo',
  },
  {
    src: '/images/services/photographer/portfolio/weddings.jpg',
    alt: 'Weddings',
  },
];

function filterReviewsBySectionName(reviews, sectionName) {
  return reviews.filter((review) => review?.section_name?.includes(sectionName));
}

const reviewsFiltered = filterReviewsBySectionName(reviews, KEY);

const metadata = {
  title: t('photographerServicePage.metadata.title'),
  description: t('photographerServicePage.metadata.description'),
};
---

<Layout metadata={metadata} noDonate>
  <Hero
    tagline={t(`services.${service.name_t}`)}
    image={{
      src: service.cover.src,
      alt: '',
    }}
  >
    <Fragment slot="title">
      {t(`services.${service.name_t}`)}
    </Fragment>

    <Fragment slot="subtitle">
      {t(`services.${service.name_t}.description`)}
    </Fragment>
  </Hero>

  {
    t(`services.${service.name_t}.intro`) && (
      <Features2 title="About the service" subtitle={t(`services.${service.name_t}.intro`)}>
        <Fragment slot="bg">
          <div class="absolute inset-0 bg-blue-50 dark:bg-transparent" />
        </Fragment>
      </Features2>
    )
  }

  {portfolioComputed.length && <Portfolio title={t(`mostRequested`)} images={portfolioComputed} />}

  <!-- {
    galleryComputed.length && (
      <PinesAlbum title="Some pictures" subtitle="from our works" imageGallery={galleryComputed} />
    )
  } -->

  <Pricing title="Flexible pricing plans" prices={prices} />

  {!!reviewsFiltered.length && <Testimonials title={t('testimonials.title')} testimonials={reviewsFiltered} />}

  {!!faqsComputed.length && <CustomFaqs faqs={faqsComputed} />}

  <ServiceWidget
    id="services-slider"
    title={t('otherServices')}
    linkText={'👉 ' + t('viewAll')}
    linkUrl={translatePath('/services')}
    serviceToExclude={KEY}
    addScript
  />
</Layout>
