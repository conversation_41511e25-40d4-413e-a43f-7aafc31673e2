# Dynamic Connector System

This document explains how to use the dynamic connector system that allows seamless switching between Airtable, NocoDB, and PocketBase data sources.

## Setup

1. **Environment Variables**: Make sure you have the following environment variables set in your `.env` file:

   **For NocoDB:**
   ```
   NOCODB_BASE_URL=https://your-nocodb-instance.com/
   NOCODB_API_TOKEN=your-api-token
   NOCODB_BASE_ID=your-base-id
   DATA_CONNECTOR=nocodb
   ```

   **For Airtable:**
   ```
   AIRTABLE_BASE_ID=your-base-id
   AIRTABLE_ACCESS_TOKEN=your-access-token
   DATA_CONNECTOR=airtable
   ```

   **For PocketBase:**
   ```
   POCKETBASE_URL=https://your-pocketbase-instance.com/
   POCKETBASE_ADMIN_EMAIL=<EMAIL>
   POCKETBASE_ADMIN_PASSWORD=your-admin-password
   DATA_CONNECTOR=pocketbase
   ```

2. **Table Configuration**: All connectors use the same `TABLE_NAMES` configuration:
   - social_agenda
   - dancing_agenda
   - reviews
   - vocabulary
   - bio
   - services
   - housing
   - settings

## Usage

### Dynamic Connector System (Recommended)

Use the dynamic connector system for seamless switching between data sources:

```javascript
// Dynamic connector - automatically routes to the correct data source
import {
    latinEvents,
    socialEvents,
    reviews,
    vocabulary,
    bio,
    settings,
    properties,
    services
} from '~/connectors/index';
```

### Switching Data Sources

To switch between data sources, simply change the `DATA_CONNECTOR` environment variable:

```bash
# Use NocoDB
DATA_CONNECTOR=nocodb

# Use Airtable
DATA_CONNECTOR=airtable

# Use PocketBase
DATA_CONNECTOR=pocketbase
```

**No code changes required!** The dynamic connector system automatically loads the appropriate connector based on the environment variable.

### Available Exports

All connectors (Airtable, NocoDB, and PocketBase) provide the same exports:

- `latinEvents` - Dancing agenda events
- `socialEvents` - Social agenda events  
- `reviews` - User reviews
- `vocabulary` - Vocabulary entries
- `bio` - Bio information
- `settings` - Application settings
- `properties` - Housing/property listings
- `services` - Service listings (with image optimization)

### API Functions

- `fetchTablesFromNocoDB()` - Fetches all configured tables from NocoDB
- `fetchTablesFromPocketBase()` - Fetches all configured tables from PocketBase

## Key Features

### PocketBase Connector Features

The PocketBase connector provides:

- **Admin Authentication**: Uses superuser credentials for full data access
- **Schema-based Field Detection**: Automatically detects field types from PocketBase collection schemas
- **Dynamic Field Transformation**: Handles file attachments, multi-select fields, and relationships
- **Comprehensive Pagination**: Fetches ALL records from collections using proper pagination
- **File URL Generation**: Converts PocketBase file references to full URLs
- **100% Drop-in Compatibility**: Same interface as Airtable and NocoDB connectors
- **Error Handling**: Graceful fallbacks and comprehensive error logging

### NocoDB Connector Features

1. **Table Label Mapping**: Uses table labels (not table names) to match against TABLE_NAMES configuration
2. **Complete Data Transformation**: Comprehensive transformation layer that converts NocoDB data to match Airtable structure exactly
3. **Attachment URL Conversion**: Automatically converts NocoDB relative paths to full URLs using NOCODB_BASE_URL
4. **Record Structure Normalization**: Transforms flat NocoDB records to Airtable's nested structure (id + fields)
5. **Relationship Mapping**: Converts NocoDB numeric IDs to Airtable's "recXXXXXXXXXXXXXX" format
6. **Date Format Standardization**: Ensures all dates are in ISO format compatible with Airtable
7. **Dynamic Multi-Select Detection**: Automatically identifies and transforms multi-select fields using intelligent heuristics
8. **Error Handling**: Includes comprehensive error handling and logging
9. **Caching**: Implements table metadata caching to reduce API calls
10. **Batch Operations**: Supports batch operations for OG image updates

## Data Transformation Details

The connector implements comprehensive data transformation to ensure 100% compatibility:

### Attachment/Image Transformation
- **NocoDB Format**: Uses `signedPath`, `path`, or `url` fields with relative paths
- **Airtable Format**: Single `url` field with absolute URLs
- **Transformation**: Automatically prefixes relative paths with `NOCODB_BASE_URL`
- **Thumbnails**: Converts NocoDB thumbnail structure to Airtable format with full URLs

### Record Structure Transformation
- **NocoDB Format**: Flat structure with all fields at root level
- **Airtable Format**: Nested structure with `id` and `fields` object
- **Transformation**: Creates proper nested structure with `get()` method for compatibility

### ID Format Transformation
- **NocoDB Format**: Numeric IDs (e.g., `1`, `2`, `3`)
- **Airtable Format**: String IDs with "rec" prefix (e.g., `rec0iqQbsJj6wKE9j`)
- **Transformation**: Uses `ncRecordId` if available, otherwise creates `rec{Id}` format

### Relationship Transformation
- **NocoDB Format**: Numeric foreign keys or arrays of objects with `Id`
- **Airtable Format**: Arrays of strings with "rec" prefix
- **Transformation**: Converts all relationship references to Airtable format

### Multi-Select Field Transformation (Dynamic Detection)
- **NocoDB Format**: Comma-separated strings (e.g., `"wifi,parking,pool"`)
- **Airtable Format**: Arrays of strings (e.g., `["wifi", "parking", "pool"]`)
- **Dynamic Detection**: Automatically identifies multi-select fields using intelligent heuristics
- **Transformation**: Converts comma-separated strings to arrays for any detected multi-select field
- **No Hardcoding**: No need to specify field names - works with any new multi-select fields
- **Smart Filtering**: Avoids false positives by detecting URLs, emails, addresses, and other non-multi-select data
- **Compatibility**: Ensures PropertyAmenities and similar components work identically with both data sources

#### Dynamic Detection Heuristics
The system uses sophisticated pattern recognition to identify multi-select fields:

**Positive Indicators (triggers transformation):**
- Contains commas separating short, simple values
- Each part is under 30 characters
- Parts contain only alphanumeric characters, spaces, hyphens, underscores
- At least 80% of parts look like typical multi-select options

**Negative Indicators (prevents transformation):**
- Contains URLs (`http`, `.com`) or email addresses (`@`)
- Contains large numbers (>100) that suggest IDs, prices, or measurements
- Contains sentence-like structure (double spaces, multiple punctuation)
- Single values without commas (unless very simple)
- Already an array (prevents re-transformation)

## Differences from Airtable

1. **Authentication**: Uses `xc-token` header instead of API key
2. **API Structure**: Uses NocoDB v2 API endpoints
3. **Data Source**: Fetches from NocoDB instead of Airtable
4. **Table Identification**: Maps table labels to table IDs using metadata API
5. **Transformation Layer**: Includes comprehensive data transformation (transparent to end user)

## Testing

The connector has been tested and verified to:
- ✅ Successfully fetch table metadata from NocoDB
- ✅ Retrieve records from all configured tables
- ✅ Transform record structure to match Airtable format exactly
- ✅ Convert attachment relative paths to full URLs
- ✅ Transform record IDs to Airtable format (rec prefix)
- ✅ Convert relationship fields to Airtable format
- ✅ Normalize date formats to ISO standard
- ✅ Dynamically detect and transform multi-select fields from comma-separated strings to arrays
- ✅ Exclude NocoDB metadata fields (CreatedAt, UpdatedAt, etc.)
- ✅ Provide the same export interface as Airtable connector
- ✅ Support get() method for field access
- ✅ Handle errors gracefully with fallbacks

## Notes

- The connector automatically handles the mapping between table labels and NocoDB table IDs
- **100% Drop-in Compatibility**: All data transformations ensure existing code works without modifications
- **Transparent Transformation**: The transformation layer is completely transparent to the end user
- **Performance Optimized**: Table metadata is cached to reduce API calls
- **URL Handling**: All relative paths are automatically converted to full URLs using NOCODB_BASE_URL
- **ID Preservation**: Uses `ncRecordId` when available to maintain original Airtable record IDs
- **Field Filtering**: Automatically excludes NocoDB-specific metadata fields
- The `mergeEvents` function works the same way for event aggregation
- OG image generation and updates are supported in production environment

## Example Transformation

**Before (NocoDB Raw Data):**
```json
{
  "Id": 1,
  "CreatedAt": "2025-07-04 00:55:03+00:00",
  "ncRecordId": "rec0iqQbsJj6wKE9j",
  "name_t": "tour_guide",
  "covers": [{
    "path": "download/2025/07/04/image.jpg",
    "signedPath": "dltemp/token/image.jpg"
  }]
}
```

**After (Airtable Compatible):**
```json
{
  "id": "rec0iqQbsJj6wKE9j",
  "fields": {
    "name_t": "tour_guide",
    "covers": [{
      "id": "ataiy2uw7wwtklff",
      "url": "https://nocodb.pulpo.cloud/dltemp/token/image.jpg",
      "filename": "image.jpg",
      "size": 171277,
      "type": "image/jpeg"
    }]
  },
  "get": function(fieldName) { return this.fields[fieldName]; }
}
```
