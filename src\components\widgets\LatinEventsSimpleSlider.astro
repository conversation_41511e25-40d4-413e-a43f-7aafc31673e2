---
import WidgetWrapper from '~/components/ui/WidgetWrapper.astro';
import Grid from '~/components/latin-events/Grid.astro';
import type { Widget } from '~/types';
import { latinEvents } from '~/connectors/index';
import Button from '~/components/ui/Button.astro';
import Item from '~/components/latin-events/GridItem.astro';

export interface Props extends Widget {
  title?: string;
  linkText?: string;
  linkUrl?: string | URL;
  information?: string;
  count?: number;
  officials?: boolean;
}

const {
  title = await Astro.slots.render('title'),
  linkText,
  // linkUrl = getPermalink('events'),
  linkUrl,
  information = await Astro.slots.render('information'),
  count = 4,
  officials = false,

  id,
  isDark = false,
  classes = {},
  bg = await Astro.slots.render('bg'),
} = Astro.props;

let latinEventsFiltered = [...latinEvents];

if (officials) latinEventsFiltered = latinEventsFiltered.filter((e) => e.flyer);
---

<WidgetWrapper id={id} isDark={isDark} containerClass={classes?.container} bg={bg}>
  <div class="flex flex-col lg:justify-between lg:flex-row mb-8">
    {
      title && (
        <div class="md:max-w-sm">
          <h2
            class="text-3xl font-bold tracking-tight sm:text-4xl sm:leading-none group font-heading mb-2"
            set:html={title}
          />
          {linkText && (
            <Button variant="link" href={linkUrl || ''}>
              {' '}
              {linkText}
            </Button>
          )}
        </div>
      )
    }

    {information && <p class="text-muted dark:text-slate-400 lg:text-sm lg:max-w-md" set:html={information} />}
  </div>

  <div id="slider-container" class="slider">
    {
      latinEventsFiltered.map((event) => (
        <div class="slide mx-4">
          <Item event={event} />
        </div>
      ))
    }

    <div id="prevButton" class="control-prev-btn">prev</div>
    <div id="nextButton" class="control-next-btn">next</div>
  </div>

  <!-- <Grid events={latinEventsFiltered} /> -->
</WidgetWrapper>

<script is:inline>
  document.querySelector('#prevButton').addEventListener('click', prev);
  document.querySelector('#nextButton').addEventListener('click', next);

  function prev() {
    document.getElementById('slider-container').scrollLeft -= 270;
  }

  function next() {
    document.getElementById('slider-container').scrollLeft += 270;
    // console.log('pressed');
  }
</script>

<style>
  .slider {
    display: flex;
    /* height: 350px; */
    max-height: auto;
    overflow-y: hidden;
    overflow-x: scroll !important;
    padding: 16px;
    transform: scroll(calc(var(--i, 0) / var(--n) * -100%));
    scroll-behavior: smooth;
  }
  .slider::-webkit-scrollbar {
    height: 5px;
    width: 150px;
    display: none;
  }
  .slider::-webkit-scrollbar-track {
    background: transparent;
  }
  .slider::-webkit-scrollbar-thumb {
    background: #888;
  }
  .slider::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
  .slider img:hover {
    transform: scale(1.05);
    box-shadow: 10px 10px 10px rgba(0, 0, 0, 0.15);
  }
  .slide {
    position: relative;
  }
  .slide img {
    height: 100%;
    width: 250px;
    margin: 0 10px;
    object-fit: cover;
    border-radius: 15px;
    cursor: pointer;
    transition: 0.25s ease-in-out;
  }
  .control-prev-btn {
    position: absolute;
    top: 50%;
    left: 0;
    background-color: rgba(255, 255, 255, 0.55);
    height: 100px;
    line-height: 100px;
    width: 45px;
    text-align: center;
    box-shadow: 0 1px 3px #888;
    user-select: none;
    color: #444;
    cursor: pointer;
  }
  .control-next-btn {
    position: absolute;
    top: 50%;
    right: 0;
    background-color: rgba(255, 255, 255, 0.55);
    height: 100px;
    line-height: 100px;
    width: 45px;
    text-align: center;
    box-shadow: 0 1px 3px #888;
    user-select: none;
    color: #444;
    cursor: pointer;
  }
  .slide img.zoomed {
    width: 500px;
    height: 600px;
    position: fixed;
    left: 25%;
    top: 0%;
    z-index: 1000;
    transform: scale(1) translatey(0) !important;
  }
  .overlay {
    position: absolute;
    height: 100%;
    width: 100%;
    background: rgba(0, 0, 0, 0.45);
    top: 0;
    display: none;
  }
  .overlay.active {
    display: block;
  }
  @media only screen and (max-width: 420px) {
    .slider {
      padding: 0;
    }
    .slide {
      padding: 16px 10px;
    }
    .slide img {
      margin: 0;
    }
    .control-prev-btn {
      top: 37%;
    }
    .control-next-btn {
      top: 37%;
    }
  }
</style>
