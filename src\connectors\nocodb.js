import { getImage } from 'astro:assets';
import { uploadImageAndGetUrl } from './pulpo.js';
import { createSocialImageWithText } from '../utils/imagesUtils.js';

const NOCODB_BASE_URL = import.meta.env.NOCODB_BASE_URL;
const NOCODB_API_TOKEN = import.meta.env.NOCODB_API_TOKEN;
const NOCODB_BASE_ID = import.meta.env.NOCODB_BASE_ID;

const TABLE_NAMES = [
    "social_agenda",
    "dancing_agenda",
    "reviews",
    "vocabulary",
    "bio",
    "services",
    "housing",
    "settings",
];

// Cache for table metadata and schemas to avoid repeated API calls
let tableMetadataCache = null;
let tableSchemasCache = new Map();

async function fetchTableMetadata() {
    if (tableMetadataCache) {
        return tableMetadataCache;
    }

    const url = `${NOCODB_BASE_URL}api/v2/meta/bases/${NOCODB_BASE_ID}/tables`;
    const response = await fetch(url, {
        headers: {
            'xc-token': NOCODB_API_TOKEN,
            'Content-Type': 'application/json'
        }
    });

    if (!response.ok) {
        throw new Error(`Failed to fetch table metadata: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    tableMetadataCache = data.list || data;
    return tableMetadataCache;
}

/**
 * Fetch detailed schema for a specific table including column information
 */
async function fetchTableSchema(tableId) {
    if (tableSchemasCache.has(tableId)) {
        return tableSchemasCache.get(tableId);
    }

    const url = `${NOCODB_BASE_URL}api/v2/meta/tables/${tableId}`;
    const response = await fetch(url, {
        headers: {
            'xc-token': NOCODB_API_TOKEN,
            'Content-Type': 'application/json'
        }
    });

    if (!response.ok) {
        throw new Error(`Failed to fetch table schema for ${tableId}: ${response.status} ${response.statusText}`);
    }

    const schema = await response.json();
    tableSchemasCache.set(tableId, schema);
    return schema;
}

/**
 * Get field type information for all tables
 */
async function getFieldTypeMap() {
    const tables = await fetchTableMetadata();
    const fieldTypeMap = new Map();

    for (const table of tables) {
        if (TABLE_NAMES.includes(table.title.toLowerCase())) {
            try {
                const schema = await fetchTableSchema(table.id);
                const tableFieldTypes = new Map();

                if (schema.columns) {
                    schema.columns.forEach(column => {
                        // Skip system fields
                        if (!column.system) {
                            tableFieldTypes.set(column.column_name, {
                                type: column.uidt,
                                title: column.title,
                                dataType: column.dt,
                                options: column.colOptions || null
                            });
                        }
                    });
                }

                fieldTypeMap.set(table.title.toLowerCase(), tableFieldTypes);
            } catch (error) {
                console.warn(`Failed to fetch schema for table ${table.title}:`, error);
                // Continue with other tables
            }
        }
    }

    return fieldTypeMap;
}

/**
 * Extract field type information from schema for a single table
 */
function extractFieldTypesFromSchema(schema) {
    const tableFieldTypes = new Map();

    if (schema && schema.columns) {
        schema.columns.forEach(column => {
            // Skip system fields
            if (!column.system) {
                tableFieldTypes.set(column.column_name, {
                    type: column.uidt,
                    title: column.title,
                    dataType: column.dt,
                    options: column.colOptions || null
                });
            }
        });
    }

    return tableFieldTypes;
}

function getTableIdByLabel(tables, label) {
    const table = tables.find(t => t.title?.toLowerCase() === label.toLowerCase());
    return table?.id || null;
}

// ===== FIELD TRANSFORMER REGISTRY =====

/**
 * Registry for field transformers based on NocoDB field types
 */
class FieldTransformerRegistry {
    constructor() {
        this.transformers = new Map();
        this.initializeDefaultTransformers();
    }

    /**
     * Register a transformer for a specific field type
     */
    register(fieldType, transformer) {
        this.transformers.set(fieldType, transformer);
    }

    /**
     * Get transformer for a field type
     */
    getTransformer(fieldType) {
        return this.transformers.get(fieldType) || this.transformers.get('default');
    }

    /**
     * Transform a field value using the appropriate transformer
     */
    transform(fieldType, value, fieldInfo = null) {
        try {
            const transformer = this.getTransformer(fieldType);
            return transformer ? transformer(value, fieldInfo) : value;
        } catch (error) {
            console.warn(`Error in field transformer for type "${fieldType}":`, error);
            // Return original value if transformation fails
            return value;
        }
    }

    /**
     * Initialize default transformers for common NocoDB field types
     */
    initializeDefaultTransformers() {
        // Attachment fields
        this.register('Attachment', (value) => {
            if (containsAttachments(value)) {
                return transformAttachments(value);
            }
            return value;
        });

        // Multi-select fields
        this.register('MultiSelect', (value) => {
            if (value) {
                return transformMultiSelect(value);
            }
            return [];
        });

        // Single select fields - return as-is (already proper format)
        this.register('SingleSelect', (value) => value);

        // Checkbox fields
        this.register('Checkbox', (value) => {
            if (typeof value === 'string') {
                return value.toLowerCase() === 'true' || value === '1';
            }
            return Boolean(value);
        });

        // Date/time fields
        this.register('CreatedTime', (value) => transformDate(value));
        this.register('LastModifiedTime', (value) => transformDate(value));
        this.register('DateTime', (value) => transformDate(value));
        this.register('Date', (value) => transformDate(value));

        // Relationship fields
        this.register('LinkToAnotherRecord', (value) => transformRelation(value));
        this.register('Links', (value) => transformRelation(value));

        // Lookup fields - return as-is
        this.register('Lookup', (value) => value);

        // Numeric fields
        this.register('Currency', (value) => {
            if (value === null || value === undefined || value === '') return null;
            return parseFloat(value) || 0;
        });
        this.register('Decimal', (value) => {
            if (value === null || value === undefined || value === '') return null;
            return parseFloat(value) || 0;
        });
        this.register('Number', (value) => {
            if (value === null || value === undefined || value === '') return null;
            return parseFloat(value) || 0;
        });

        // Text fields - return as-is
        this.register('SingleLineText', (value) => value);
        this.register('LongText', (value) => value);
        this.register('Email', (value) => value);
        this.register('URL', (value) => value);
        this.register('PhoneNumber', (value) => value);

        // System fields
        this.register('ID', (value) => value);
        this.register('CreatedBy', (value) => value);
        this.register('LastModifiedBy', (value) => value);
        this.register('Order', (value) => value);

        // Default transformer for unknown types
        this.register('default', (value) => {
            // Fallback to dynamic detection for unknown field types
            if (containsAttachments(value)) {
                return transformAttachments(value);
            }
            if (containsMultiSelect(value)) {
                return transformMultiSelect(value);
            }
            return value;
        });
    }
}

// Create global transformer registry instance
const fieldTransformerRegistry = new FieldTransformerRegistry();

// ===== DATA TRANSFORMATION FUNCTIONS =====

/**
 * Transform NocoDB attachment to Airtable format
 */
function transformAttachment(nocoAttachment) {
    if (!nocoAttachment || typeof nocoAttachment !== 'object') {
        return null;
    }

    // Priority: signedPath (temporary) > url (if present) > path (permanent)
    let url = nocoAttachment.signedPath || nocoAttachment.url || nocoAttachment.path;

    // If it's a relative path, make it absolute using NOCODB_BASE_URL
    if (url && !url.startsWith('http')) {
        url = `${NOCODB_BASE_URL}${url}`;
    }

    if (!url) {
        console.warn('Attachment without valid URL:', nocoAttachment);
        return null;
    }

    // Transform thumbnails to Airtable format
    const thumbnails = {};
    if (nocoAttachment.thumbnails) {
        Object.entries(nocoAttachment.thumbnails).forEach(([size, thumb]) => {
            if (thumb && thumb.signedPath) {
                let thumbUrl = thumb.signedPath;
                if (!thumbUrl.startsWith('http')) {
                    thumbUrl = `${NOCODB_BASE_URL}${thumbUrl}`;
                }
                thumbnails[size] = {
                    url: thumbUrl,
                    width: thumb.width || 0,
                    height: thumb.height || 0
                };
            }
        });
    }

    return {
        id: nocoAttachment.id || `att${Date.now()}${Math.random().toString(36).substring(2, 11)}`,
        width: nocoAttachment.width || 0,
        height: nocoAttachment.height || 0,
        url: url,
        filename: nocoAttachment.title || nocoAttachment.filename || 'unknown',
        size: nocoAttachment.size || 0,
        type: nocoAttachment.mimetype || nocoAttachment.type || 'unknown',
        thumbnails: thumbnails
    };
}

/**
 * Check if an object looks like a NocoDB attachment
 */
function isNocoDBAttachment(obj) {
    if (!obj || typeof obj !== 'object') {
        return false;
    }

    // Check for characteristic NocoDB attachment properties
    const hasPathProperties = obj.hasOwnProperty('path') || obj.hasOwnProperty('signedPath') || obj.hasOwnProperty('url');
    const hasMimeType = obj.hasOwnProperty('mimetype') || obj.hasOwnProperty('type');
    const hasFileProperties = obj.hasOwnProperty('size') || obj.hasOwnProperty('filename') || obj.hasOwnProperty('title');
    const hasImageProperties = obj.hasOwnProperty('width') || obj.hasOwnProperty('height');
    const hasThumbnails = obj.hasOwnProperty('thumbnails');

    // An object is likely a NocoDB attachment if it has:
    // 1. At least one path/URL property AND
    // 2. At least one other characteristic property (mimetype, size, dimensions, etc.)
    return hasPathProperties && (hasMimeType || hasFileProperties || hasImageProperties || hasThumbnails);
}

/**
 * Check if a value contains NocoDB attachments (single or array)
 */
function containsAttachments(value) {
    if (!value) return false;

    // Handle string JSON
    if (typeof value === 'string') {
        try {
            value = JSON.parse(value);
        } catch (e) {
            return false;
        }
    }

    // Check single object
    if (typeof value === 'object' && !Array.isArray(value)) {
        return isNocoDBAttachment(value);
    }

    // Check array - consider it attachments if at least one item is an attachment
    if (Array.isArray(value) && value.length > 0) {
        return value.some(item => isNocoDBAttachment(item));
    }

    return false;
}

/**
 * Check if a string value appears to be a multi-select field (comma-separated values)
 * Uses heuristics to identify likely multi-select fields while avoiding false positives
 */
function isMultiSelectString(value) {
    if (typeof value !== 'string' || !value.trim()) {
        return false;
    }

    // Don't transform if it's clearly not a multi-select:
    // 1. Contains numbers that look like IDs, prices, or measurements
    // 2. Contains special characters that suggest it's a URL, email, or formatted text
    // 3. Is a single word without commas
    // 4. Contains sentence-like structure (multiple spaces, punctuation patterns)

    const trimmedValue = value.trim();

    // If no commas, it's likely not a multi-select (unless it's a single option)
    if (!trimmedValue.includes(',')) {
        // Single values could be multi-select with one option, but we need to be careful
        // Only consider it multi-select if it's a simple word/phrase without special patterns
        return /^[a-zA-Z0-9_\/\-\s]+$/.test(trimmedValue) &&
            trimmedValue.length < 50 &&
            !trimmedValue.includes('  ') && // No double spaces
            !/\d{2,}/.test(trimmedValue); // No long numbers
    }

    // Split by comma and analyze the parts
    const parts = trimmedValue.split(',').map(part => part.trim()).filter(part => part.length > 0);

    // Must have at least 2 parts to be considered multi-select
    if (parts.length < 2) {
        return false;
    }

    // Check if parts look like multi-select options
    const validParts = parts.filter(part => {
        // Each part should be:
        // - Not too long (multi-select options are usually short)
        // - Not contain complex patterns (URLs, emails, etc.)
        // - Not be purely numeric (unless it's clearly categorical)
        // - Not contain sentence-like structure

        if (part.length > 30) return false; // Too long for typical multi-select option
        if (part.includes('http') || part.includes('@') || part.includes('.com')) return false; // URLs/emails

        // Corrected regex: detect numeric strings that are likely not categories
        if (/^\d+(\.\d+)?$/.test(part) && parseFloat(part) > 100) return false; // Large numbers (prices, IDs, etc.)

        if (part.includes('  ')) return false; // Double spaces suggest sentences
        if (/[.!?]{2,}/.test(part)) return false; // Multiple punctuation

        return true;
    });

    // At least 80% of parts should look like valid multi-select options
    return validParts.length >= Math.ceil(parts.length * 0.8);
}

/**
 * Check if a value contains multi-select data that should be transformed to an array
 */
function containsMultiSelect(value) {
    if (!value) return false;

    // Only process strings that look like multi-select fields
    if (typeof value === 'string') {
        return isMultiSelectString(value);
    }

    // If it's already an array, check if it contains simple string values
    // that suggest it might be a multi-select field that's already been processed
    if (Array.isArray(value) && value.length > 0) {
        // If all items are simple strings (not objects), it might be a multi-select
        // But we don't want to re-transform it, so return false
        return false;
    }

    return false;
}

/**
 * Transform array of NocoDB attachments to Airtable format
 */
function transformAttachments(nocoAttachments) {
    if (!nocoAttachments) return [];

    // Handle both array and string JSON
    let attachments = nocoAttachments;
    if (typeof attachments === 'string') {
        try {
            attachments = JSON.parse(attachments);
        } catch (e) {
            console.warn('Error parsing JSON attachments:', e);
            return [];
        }
    }

    if (!Array.isArray(attachments)) {
        attachments = [attachments];
    }

    return attachments
        .map(transformAttachment)
        .filter(Boolean);
}

/**
 * Transform NocoDB multi-select field to array format
 */
function transformMultiSelect(nocoMultiSelect) {
    if (!nocoMultiSelect) return [];

    // If it's already an array, return as-is
    if (Array.isArray(nocoMultiSelect)) {
        return nocoMultiSelect.filter(item => item !== null && item !== undefined && item !== '');
    }

    // If it's a string, split by comma and clean up
    if (typeof nocoMultiSelect === 'string') {
        return nocoMultiSelect
            .split(',')
            .map(item => item.trim())
            .filter(item => item.length > 0);
    }

    // For other types, return empty array
    return [];
}

/**
 * Transform NocoDB relationship to Airtable format
 */
function transformRelation(nocoRelation) {
    if (!nocoRelation) return [];

    // If it's a number (foreign key), convert to Airtable format
    if (typeof nocoRelation === 'number') {
        return [`rec${nocoRelation}`];
    }

    // If it's a string, try to parse it
    if (typeof nocoRelation === 'string') {
        try {
            const parsed = JSON.parse(nocoRelation);
            return transformRelation(parsed);
        } catch (e) {
            // If not JSON, might be a single ID
            return [`rec${nocoRelation}`];
        }
    }

    // If it's an array of objects with Id
    if (Array.isArray(nocoRelation)) {
        return nocoRelation.map(item => {
            if (typeof item === 'object' && item.Id) {
                return `rec${item.Id}`;
            }
            return `rec${item}`;
        });
    }

    return [];
}

/**
 * Transform NocoDB date to Airtable format
 */
function transformDate(nocoDate) {
    if (!nocoDate) return null;

    // NocoDB dates are usually in ISO format, which Airtable also uses
    // Just ensure it's a proper ISO string
    try {
        const date = new Date(nocoDate);
        return date.toISOString();
    } catch (e) {
        console.warn('Invalid date format:', nocoDate);
        return nocoDate; // Return as-is if can't parse
    }
}

/**
 * Transform a field value based on schema information and field type
 */
function transformField(fieldName, value, fieldTypeInfo = null) {
    if (value === null || value === undefined) {
        return null;
    }

    // Handle empty arrays or arrays with null values
    if (Array.isArray(value)) {
        // Filter out null values from arrays
        const filteredValue = value.filter(item => item !== null && item !== undefined);
        if (filteredValue.length === 0) {
            return [];
        }
        // Update value to the filtered array for further processing
        value = filteredValue;
    }

    // Use schema-based transformation if field type information is available
    if (fieldTypeInfo && fieldTypeInfo.type) {
        try {
            return fieldTransformerRegistry.transform(fieldTypeInfo.type, value, fieldTypeInfo);
        } catch (error) {
            console.warn(`Error in schema-based transformation for field "${fieldName}" (type: ${fieldTypeInfo.type}):`, error);
            // Fall through to legacy transformation
        }
    }

    // Legacy transformation logic as fallback
    // Dynamic attachment detection - check if the value contains attachment objects
    if (containsAttachments(value)) {
        return transformAttachments(value);
    }

    // Dynamic multi-select detection - check if the value contains multi-select data
    if (containsMultiSelect(value)) {
        return transformMultiSelect(value);
    }

    // Handle relationship fields - but be more selective
    // Only transform fields that are explicitly relationship fields, not simple numeric fields
    if (fieldName.includes('_link') || fieldName.includes('_relation') || fieldName.startsWith('_nc_m2m_')) {
        return transformRelation(value);
    }

    // Handle date fields
    if (fieldName.includes('date') || fieldName.includes('Date') || fieldName === 'CreatedAt' || fieldName === 'UpdatedAt') {
        return transformDate(value);
    }

    // Handle arrays of objects with Id (relationships) - but only if they're actually relationship objects
    if (Array.isArray(value) && value.length > 0 && typeof value[0] === 'object' &&
        value[0] !== null && value[0].hasOwnProperty('Id') && Object.keys(value[0]).length > 1) {
        return transformRelation(value);
    }

    // Handle special array cases
    if (Array.isArray(value) && value.length === 1 && value[0] === null) {
        return [null];
    }
    if (Array.isArray(value) && value.length === 1 && value[0] === "/n") {
        return [null];
    }

    // Special handling for language fields in vocabulary table
    // Preserve language fields (en, es, it, de, ru, uk, fr) as-is
    if (fieldName === 'en' || fieldName === 'es' || fieldName === 'it' ||
        fieldName === 'de' || fieldName === 'ru' || fieldName === 'uk' || fieldName === 'fr') {
        return value;
    }

    // Handle string fields - ensure they're not null/undefined to prevent limax errors
    // Also ensure arrays are not passed to string processing functions
    if (typeof value === 'string') {
        return value;
    }

    // For non-string, non-null values, return as-is (but ensure they're not arrays that should be strings)
    if (value !== null && value !== undefined && !Array.isArray(value)) {
        return value;
    }

    // For arrays, return as-is (they've already been processed above if needed)
    if (Array.isArray(value)) {
        return value;
    }

    // For simple numeric fields like 'location', keep them as-is
    // Don't transform simple numbers to relationship arrays
    return value;
}

/**
 * Transform complete NocoDB record to Airtable format
 */
function transformRecord(nocoRecord, tableFieldTypes = null) {
    if (!nocoRecord || typeof nocoRecord !== 'object') {
        return null;
    }

    // Use ncRecordId if present (maintains original Airtable ID)
    // Otherwise use Id with "rec" prefix
    const recordId = nocoRecord.ncRecordId || `rec${nocoRecord.Id || nocoRecord.id || Date.now()}`;

    // Fields to exclude from transformation
    const excludeFields = [
        'Id', 'id', 'CreatedAt', 'UpdatedAt', 'ncRecordId', 'ncRecordHash'
    ];

    // Transform all fields
    const fields = {};
    Object.entries(nocoRecord).forEach(([key, value]) => {
        if (!excludeFields.includes(key)) {
            try {
                // Get field type information from schema if available
                const fieldTypeInfo = tableFieldTypes ? tableFieldTypes.get(key) : null;
                fields[key] = transformField(key, value, fieldTypeInfo);
            } catch (error) {
                console.warn(`Error transforming field "${key}":`, error);
                // Keep the original value if transformation fails
                fields[key] = value;
            }
        }
    });

    return {
        id: recordId,
        fields: fields,
        get: function (fieldName) {
            return this.fields[fieldName];
        }
    };
}

function mergeEvents(record) {
    const otherEvents = record.get('other_events') || [];
    const fixedEvents = record.get('fixed_events') || [];
    return [...otherEvents, ...fixedEvents];
}

async function createOgImage(record) {
    const mergedEvents = mergeEvents(record);
    const eventCount = mergedEvents.length;
    const textComputed = eventCount > 1 ? `${eventCount} eventos` : eventCount === 1 ? "1 evento" : "No hay eventos";

    const imageBuffer = await createSocialImageWithText(textComputed);
    const imageUrl = await uploadImageAndGetUrl(imageBuffer);

    return {
        id: record.id,
        fields: {
            'og_image': [{ url: imageUrl, filename: `event-${record.id}.png` }]
        }
    };
}

async function updateOgImages(tableId) {
    // Fetch table schema for field type information
    let tableFieldTypes = null;
    try {
        const schema = await fetchTableSchema(tableId);
        tableFieldTypes = extractFieldTypesFromSchema(schema);
    } catch (schemaError) {
        console.warn(`Failed to fetch schema for table ${tableId}, falling back to dynamic detection:`, schemaError);
    }

    // Fetch ALL records using pagination
    let allRecords = [];
    let offset = 0;
    const limit = 100;
    let hasMoreRecords = true;

    while (hasMoreRecords) {
        const url = `${NOCODB_BASE_URL}api/v2/tables/${tableId}/records?fields=other_events,fixed_events&limit=${limit}&offset=${offset}`;
        const response = await fetch(url, {
            headers: {
                'xc-token': NOCODB_API_TOKEN,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`Failed to fetch records for OG image update: ${response.status}`);
        }

        const data = await response.json();
        const records = data.list || data;

        if (records && records.length > 0) {
            allRecords = allRecords.concat(records);
            offset += limit;
            hasMoreRecords = records.length === limit;
        } else {
            hasMoreRecords = false;
        }
    }

    // Transform records to have the proper structure with get() method using schema information
    const transformedRecords = allRecords.map(record => transformRecord(record, tableFieldTypes)).filter(Boolean);
    const updates = await Promise.all(transformedRecords.map(createOgImage));

    // Update records in batches of 10
    for (let i = 0; i < updates.length; i += 10) {
        const batch = updates.slice(i, i + 10);
        for (const update of batch) {
            const updateUrl = `${NOCODB_BASE_URL}api/v2/tables/${tableId}/records/${update.id}`;
            await fetch(updateUrl, {
                method: 'PATCH',
                headers: {
                    'xc-token': NOCODB_API_TOKEN,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(update.fields)
            });
        }
    }

    console.log(`OG_IMAGES UPDATED SUCCESSFULLY for table ${tableId}`);
}

async function bootstrapOgImages(tableLabels) {
    const tables = await fetchTableMetadata();

    for (const label of tableLabels) {
        const tableId = getTableIdByLabel(tables, label);
        if (tableId) {
            await updateOgImages(tableId);
        }
    }
}

async function fetchDataFromTable(tableLabel) {
    const tableLabelSanitized = tableLabel.toLowerCase();
    if (!TABLE_NAMES.includes(tableLabelSanitized)) return null;

    try {
        const tables = await fetchTableMetadata();
        const tableId = getTableIdByLabel(tables, tableLabel);

        if (!tableId) {
            console.warn(`Table with label "${tableLabel}" not found`);
            return { table: tableLabelSanitized, data: [] };
        }

        // Fetch table schema for field type information
        let tableFieldTypes = null;
        try {
            const schema = await fetchTableSchema(tableId);
            tableFieldTypes = extractFieldTypesFromSchema(schema);
        } catch (schemaError) {
            console.warn(`Failed to fetch schema for table ${tableLabel}, falling back to dynamic detection:`, schemaError);
        }

        // Fetch ALL records using pagination
        let allRecords = [];
        let offset = 0;
        const limit = 100; // Fetch 100 records per request for efficiency
        let hasMoreRecords = true;

        while (hasMoreRecords) {
            const url = `${NOCODB_BASE_URL}api/v2/tables/${tableId}/records?limit=${limit}&offset=${offset}`;
            const response = await fetch(url, {
                headers: {
                    'xc-token': NOCODB_API_TOKEN,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`Failed to fetch data from table ${tableLabel}: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            const records = data.list || data;

            if (records && records.length > 0) {
                allRecords = allRecords.concat(records);
                offset += limit;

                // Check if we got fewer records than requested, indicating we've reached the end
                hasMoreRecords = records.length === limit;
            } else {
                hasMoreRecords = false;
            }
        }

        console.log(`Fetched ${allRecords.length} records from table ${tableLabel}`);

        // Transform NocoDB records to match Airtable structure using schema information
        const transformStart = Date.now();
        const transformedRecords = allRecords.map(record => transformRecord(record, tableFieldTypes)).filter(Boolean);
        const transformTime = Date.now() - transformStart;

        if (tableFieldTypes) {
            console.log(`Transformed ${transformedRecords.length} records using schema-based detection in ${transformTime}ms`);
        } else {
            console.log(`Transformed ${transformedRecords.length} records using dynamic detection in ${transformTime}ms`);
        }

        return { table: tableLabelSanitized, data: transformedRecords };
    } catch (error) {
        console.error(`Error fetching data from table ${tableLabel}:`, error);
        return { table: tableLabelSanitized, data: [] };
    }
}

export async function fetchTablesFromNocoDB() {
    return Promise.all(TABLE_NAMES.map(fetchDataFromTable));
}

const normalizeRecord = (record) => {
    // Record is already transformed to Airtable format with id, fields, and get() method
    const mergedEvents = mergeEvents(record);
    const normalized = {
        id: record.id,
        ...record.fields,
        other_events: mergedEvents  // Replace other_events with merged events
    };



    return normalized;
};



// Simple approach with timeout protection
async function initializeWithTimeout() {
    const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Initialization timeout')), 10000); // 10 second timeout
    });

    const initPromise = (async () => {
        if (process.env.NODE_ENV === 'production') {
            bootstrapOgImages(['dancing_agenda', 'social_agenda']);
            console.log(`${process.env.NODE_ENV} Environment`);
        }

        const tables = await fetchTablesFromNocoDB();
        const getTable = (tableName) => tables.find((t) => t.table === tableName)?.data || [];

        const servicesRaw = getTable('services').map(normalizeRecord).filter(s => s.published);
        const processedServices = await Promise.all(servicesRaw.map(async (s) => {
            try {
                if (s.covers && s.covers[0]?.url) {
                    return {
                        ...s,
                        cover: await getImage({ src: s.covers[0].url, format: 'webp', width: 1080, height: 1080 })
                    };
                }
                return s;
            } catch (error) {
                console.warn('Error processing service image:', error);
                return s;
            }
        }));

        return {
            latinEvents: getTable('dancing_agenda').map(normalizeRecord),
            socialEvents: getTable('social_agenda').map(normalizeRecord),
            reviews: getTable('reviews').map(normalizeRecord),
            vocabulary: getTable('vocabulary').map(normalizeRecord),
            bio: getTable('bio').map(normalizeRecord),
            settings: getTable('settings').map(normalizeRecord),
            properties: getTable('housing').map(normalizeRecord),
            services: processedServices
        };
    })();

    return Promise.race([initPromise, timeoutPromise]);
}

let data;
try {
    data = await initializeWithTimeout();
    console.log('NocoDB data loaded successfully');
} catch (error) {
    console.error('Failed to load NocoDB data:', error);
    data = {
        latinEvents: [],
        socialEvents: [],
        reviews: [],
        vocabulary: [],
        bio: [],
        settings: [],
        properties: [],
        services: []
    };
}

export const latinEvents = data.latinEvents;
export const socialEvents = data.socialEvents;
export const reviews = data.reviews;
export const vocabulary = data.vocabulary;
export const bio = data.bio;
export const settings = data.settings;
export const properties = data.properties;
export const services = data.services;
