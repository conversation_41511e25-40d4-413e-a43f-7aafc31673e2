---
import Layout from '~/layouts/Layout.astro';
import Header from '~/components/widgets/Header.astro';
import Footer from '~/components/widgets/Footer.astro';

import { headerData, footerData } from '~/navigation';
import { getLangFromUrl } from '~/i18n/utils';

import type { MetaData } from '~/types';
import DonateNoteButton from '~/components/common/DonateNoteButton.astro';
import Popup from '~/components/widgets/Popup.astro';

export interface Props {
  metadata?: MetaData;
  noDonate?: boolean;
}

const { metadata, noDonate } = Astro.props;

// Page Setup
const lang = getLangFromUrl(Astro.url);

const allowedRoutes = ['/', '/dancing', '/es/baile', '/socialization', '/es/socializacion'];
const exactRoutes = ['/es'];

// const popupImageAltText = 'Follow us on Socials';
const popupImageAltText = 'Social Meeting!';
const popupImageUrl = 'https://social-meeting-livegrancanaria.netlify.app/assets/images/social-2-1080x1080.png';
const popupLinkUrl =
  lang === 'es'
    ? 'https://social-meeting-livegrancanaria.netlify.app/index_es'
    : 'https://social-meeting-livegrancanaria.netlify.app/';
// const popupCountdownText = 'countdown.text';
const popupPosition = 'bottom-right';
// const countdownEndDate = '2024/10/30 23:59:59';
const hideDays = 3;
---

<Layout metadata={metadata}>
  <slot name="header">
    <Header {...headerData} isSticky showToggleTheme />
  </slot>
  <main>
    <slot />
  </main>

  <!-- <DonateNoteButton link={import.meta.env.KO_FI_LINK} targetBlank /> -->
  {noDonate ? '' : <DonateNoteButton link={import.meta.env.KO_FI_LINK} targetBlank />}

  <!-- <Popup
    allowedRoutes={allowedRoutes}
    exactRoutes={exactRoutes}
    hideDays={hideDays}
    position={popupPosition}
    countdownEndDate={countdownEndDate}
    countdownText={popupCountdownText}
    imageUrl={popupImageUrl}
    altText={popupImageAltText}
    linkUrl={popupLinkUrl}
    fullscreen={false}
  > -->

  <Popup
    allowedRoutes={allowedRoutes}
    exactRoutes={exactRoutes}
    hideDays={hideDays}
    position={popupPosition}
    imageUrl={popupImageUrl}
    linkUrl={popupLinkUrl}
    fullscreen={false}
    openInNewTab
  >
    <!-- <PopupFollowUs /> -->
  </Popup>

  <slot name="footer">
    <Footer {...footerData} />
  </slot>
</Layout>
