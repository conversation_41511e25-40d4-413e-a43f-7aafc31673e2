---
import { getLangFromUrl, useTranslations, useTranslatedPath } from '~/i18n/utils';
import slugify from 'limax';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);

export interface Props {
  dayOfTheWeek: number;
  pathPrefix: string;
}

const { dayOfTheWeek = null, pathPrefix } = Astro.props;

const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

const computeDay = (day: string): string => {
  const translatedDay = t(day.toLowerCase());
  const safeTranslatedDay = translatedDay || day.toLowerCase();
  const path = `${pathPrefix}/${slugify(safeTranslatedDay.toLowerCase())}`;
  return translatePath(path);
};

const isCurrentDay = (index: number): boolean => +dayOfTheWeek - 1 === index;

const getDayClass = (index: number): string => {
  const baseClass =
    'px-4 py-2 mx-1 text-gray-700 transition-colors duration-300 transform rounded-md sm:inline dark:text-gray-200';
  return isCurrentDay(index)
    ? `${baseClass} border-orange text-gray-500 bg-white cursor-not-allowed dark:bg-gray-800 dark:text-gray-600`
    : `${baseClass} hover:bg-orange-800 dark:hover:bg-orange-800 hover:text-white dark:hover:text-gray-200`;
};
---

<div class="w-full bg-white dark:bg-gray-800">
  <div class="container flex items-center justify-center px-6 py-5 mx-auto">
    <div class="flex">
      {
        dayOfTheWeek &&
          days.map((day, i) => (
            <a href={isCurrentDay(i) ? '#' : computeDay(day)} class={getDayClass(i)}>
              <span class="hidden md:block">{(t(day.toLowerCase()) || day).substring(0, 3)}</span>
              <span class="md:hidden">{(t(day.toLowerCase()) || day).substring(0, 1)}</span>
            </a>
          ))
      }
    </div>
  </div>
</div>

<style>
  .border-orange {
    box-shadow: 0 0 0.1rem 0.1rem orange;
  }
</style>
