---
import ImgHostedWithSlideshow from '../common/ImgHostedWithSlideshow.astro';
import { getLangFromUrl, useTranslations } from '~/i18n/utils';

interface Flyer {
  url: string;
}

interface Props {
  flyers: Flyer[];
}

// i18n setup
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

const { flyers } = Astro.props;

// Filter out any flyers without valid URLs
const validFlyers = flyers?.filter((flyer) => flyer?.url) || [];
---

<section class="my-4 p-4 border border-dashed border-gray-600" aria-labelledby="flyers-section-title">
  <h2 id="flyers-section-title" class="sr-only">Event Flyers Gallery</h2>

  {
    validFlyers.length > 0 && (
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        {validFlyers.map((flyer, index) => (
          <ImgHostedWithSlideshow
            prefix={t('flyer').toLowerCase()}
            flyerUrl={flyer.url}
            index={index}
            length={validFlyers.length}
          />
        ))}
      </div>
    )
  }

  <div
    class="flex w-full overflow-hidden mt-8 border border-gray-200 dark:border-gray-700 bg-white dark:bg-slate-900 shadow"
  >
    <div class="flex items-center justify-center w-12 bg-red-800 text-white">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="w-10 h-10 text-white inline-block m-2"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fill-rule="evenodd"
          d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
          clip-rule="evenodd"></path>
      </svg>
    </div>

    <div class="px-4 -mx-3">
      <div class="mx-3">
        <p class="text-sm text-gray-600 dark:text-gray-200 py-2" set:html={t('otherEvents.disclaimer')} />
      </div>
    </div>
  </div>
</section>
