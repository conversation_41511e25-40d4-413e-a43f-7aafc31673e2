---
import '~/assets/styles/tailwind.css';

import { I18N } from '~/utils/config';

import CommonMeta from '~/components/common/CommonMeta.astro';
import Favicons from '~/components/Favicons.astro';
import CustomStyles from '~/components/CustomStyles.astro';
import ApplyColorMode from '~/components/common/ApplyColorMode.astro';
import Metadata from '~/components/common/Metadata.astro';
import BasicScripts from '~/components/common/BasicScripts.astro';
// import SiteVerification  from "~/components/common/SiteVerification.astro"
// import Analytics  from "~/components/common/Analytics.astro"

import type { MetaData as MetaDataType } from '~/types';
import CookieConsent from '~/components/common/CookieConsent.astro';
import NoCache from '~/components/common/NoCache.astro';

import { getLangFromUrl } from '../i18n/utils';
// import RemoveSlash from '~/components/common/RemoveSlash.astro';

const lang = getLangFromUrl(Astro.url);

export interface Props {
  metadata?: MetaDataType;
}

const { metadata = {} } = Astro.props;
const { language, textDirection } = I18N;
---

<!doctype html>
<!-- <html lang={language} dir={textDirection} class="2xl:text-[20px]"> -->
<html lang={lang} dir={textDirection} class="2xl:text-[20px]">
  <head>
    <NoCache />
    <CommonMeta />
    <Favicons />
    <CustomStyles />
    <ApplyColorMode />
    <Metadata {...metadata} />
    <CookieConsent />
    <!-- <RemoveSlash/> -->
    <!-- <SiteVerification /> -->
    <!-- <Analytics /> -->

    <!-- Meta Domain Verification -->
    <meta name="facebook-domain-verification" content="tvmg7uhjumt8zxboh2d34u1f3rsy8q" />
    <!-- Meta Pixel Code -->
    <script>
      !(function (f, b, e, v, n, t, s) {
        if (f.fbq) return;
        n = f.fbq = function () {
          n.callMethod ? n.callMethod.apply(n, arguments) : n.queue.push(arguments);
        };
        if (!f._fbq) f._fbq = n;
        n.push = n;
        n.loaded = !0;
        n.version = '2.0';
        n.queue = [];
        t = b.createElement(e);
        t.async = !0;
        t.src = v;
        s = b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t, s);
      })(window, document, 'script', 'https://connect.facebook.net/en_US/fbevents.js');
      fbq('init', '1062911722008197');
      fbq('track', 'PageView');
    </script>
    <noscript
      ><img
        height="1"
        width="1"
        style="display:none"
        src="https://www.facebook.com/tr?id=1062911722008197&ev=PageView&noscript=1"
      /></noscript
    >
    <!-- End Meta Pixel Code -->
  </head>

  <body class="antialiased text-default bg-page tracking-tight">
    <slot />

    <BasicScripts />

    <style is:global>
      img {
        content-visibility: auto;
      }
    </style>
  </body>
</html>
