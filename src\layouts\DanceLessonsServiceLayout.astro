---
import Layout from '~/layouts/PageLayout.astro';
import { services, vocabulary, reviews } from '~/connectors/index';
import CustomFaqs from '~/components/common/CustomFaqs.astro';
import Testimonials from '~/components/widgets/Testimonials.astro';
import Hero from '~/components/widgets/Hero.astro';
import Features2 from '~/components/widgets/Features2.astro';
import ServiceWidget from '~/components/services/ServiceWidget.astro';

import { getLangFromUrl, useTranslations, useTranslatedPath } from '~/i18n/utils';
import Pricing from '~/components/widgets/Pricing.astro';
import ContactOptions from '~/components/common/ContactOptions.astro';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);

const KEY = 'dance_lessons';

const service = services.find((s) => s.name_t === KEY);
const faqs = vocabulary.filter((item) => item.key.startsWith(`faqs.${KEY}`));

const prices = [
  {
    title: 'Try-Out Package',
    subtitle: '1 hour of private 1on1 dance class',
    period: '/ one time',
    callToAction: {
      targetBlank: true,
      text: "Let's go!",
      href: '#',
    },
  },
  {
    title: 'Bronze Package',
    subtitle: '4 hours of private 1on1 dance classes',
    period: '/ one time',
    callToAction: {
      targetBlank: true,
      text: "Let's go!",
      href: '#',
    },
    hasRibbon: true,
    ribbonTitle: 'popular',
  },
  {
    title: 'Gold Package',
    subtitle: '10 hours of private 1on1 dance classes',
    period: '/ one time',
    callToAction: {
      targetBlank: true,
      text: "Let's go!",
      href: '#',
    },
  },
];

let temp = {};
faqs.forEach((faq) => {
  let number = faq.key.split('.').pop();
  if (!temp[number]) temp[number] = {};
  if (faq.key.includes('question')) {
    temp[number].question = faq.key;
  } else if (faq.key.includes('answer')) {
    temp[number].answer = faq.key;
  }
});

let faqsComputed = Object.values(temp);

function filterReviewsBySectionName(reviews, sectionName) {
  return reviews.filter((review) => review?.section_name?.includes(sectionName));
}

const reviewsFiltered = filterReviewsBySectionName(reviews, KEY);

const metadata = {
  title: t('danceLessonsServicePage.metadata.title'),
  description: t('danceLessonsServicePage.metadata.description'),
};

const contactOptions = [
  {
    type: 'whatsapp',
    icon: 'tabler:brand-whatsapp',
    title: 'WhatsApp',
    description: t('contactPage.whatsapp.description'),
    cta: t('contactPage.whatsapp.cta'),
    href: `https://wa.me/${import.meta.env.WHATSAPP_SUPPORT}`,
    bgColor: 'bg-green-500',
    hoverColor: 'hover:bg-green-700',
  },
  {
    type: 'email',
    icon: 'tabler:mail',
    title: t('contactPage.email'),
    description: t('contactPage.email.description'),
    cta: t('contactPage.email.cta'),
    href: `mailto:${import.meta.env.MAIL_SUPPORT}`,
    bgColor: 'bg-indigo-500',
    hoverColor: 'hover:bg-indigo-700',
  },
  {
    type: 'phone',
    icon: 'tabler:phone',
    title: t('contactPage.phone'),
    description: t('contactPage.phone.description'),
    cta: t('contactPage.phone.cta'),
    href: `tel:${import.meta.env.PHONE_SUPPORT}`,
    bgColor: 'bg-blue-500',
    hoverColor: 'hover:bg-blue-700',
  },
];
---

<Layout metadata={metadata} noDonate>
  <Hero
    tagline={t(`services.${service.name_t}`)}
    image={{
      src: service.cover.src,
      alt: '',
    }}
    callToAction={{
      text: 'Get template',
      href: 'https://github.com/onwidget/astrowind',
      icon: 'tabler:download',
    }}
  >
    <Fragment slot="title">
      {t(`services.${service.name_t}`)}
    </Fragment>

    <Fragment slot="subtitle">
      {t(`services.${service.name_t}.description`)}

      <!-- <div class="container mx-auto px-4 py-4 flex justify-between items-center">
        <div class="flex items-center">
          <nav>
            <ul class="flex space-x-6">
              <li>
                <a href="#classes" class="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
                  >Classes</a
                >
              </li>
              <li>
                <a href="#packages" class="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
                  >Packages</a
                >
              </li>
              <li>
                <a href="#reviews" class="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
                  >Reviews</a
                >
              </li>
              <li>
                <a href="#faq" class="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">FAQ</a
                >
              </li>
              <li>
                <a href="#contact" class="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
                  >Contact</a
                >
              </li>
            </ul>
          </nav>
        </div>
        <div class="flex items-center space-x-4">
          <button class="bg-blue-500 text-white px-6 py-2 rounded-full hover:bg-blue-600 transition duration-300"
            >Book a Class</button
          >
        </div>
      </div> -->
    </Fragment>
  </Hero>

  {
    t(`services.${service.name_t}.intro`) && (
      <Features2 title="About the service" subtitle={t(`services.${service.name_t}.intro`)}>
        <Fragment slot="bg">
          <div class="absolute inset-0 bg-blue-50 dark:bg-transparent" />
        </Fragment>
      </Features2>
    )
  }

  <!-- Classes Section -->
  <section id="classes" class="py-16">
    <div class="container mx-auto px-4">
      <h2 class="text-3xl font-bold text-center mb-12">Our Dance Classes</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
          <img src="https://placehold.co/600x400" alt="Ballet class" class="w-full h-48 object-cover" />
          <div class="p-6">
            <h3 class="text-xl font-semibold mb-2">Salsa</h3>
            <p class="text-gray-600 dark:text-gray-400">Master the grace and technique of classical ballet.</p>
          </div>
        </div>
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
          <img src="https://placehold.co/600x400" alt="Modern dance class" class="w-full h-48 object-cover" />
          <div class="p-6">
            <h3 class="text-xl font-semibold mb-2">Bachata</h3>
            <p class="text-gray-600 dark:text-gray-400">Express yourself through fluid and contemporary movements.</p>
          </div>
        </div>
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
          <img src="https://placehold.co/600x400" alt="Hip-hop class" class="w-full h-48 object-cover" />
          <div class="p-6">
            <h3 class="text-xl font-semibold mb-2">Kizomba</h3>
            <p class="text-gray-600 dark:text-gray-400">Learn the latest street dance styles and techniques.</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <Pricing title="Flexible pricing plans" prices={prices} />

  {!!reviewsFiltered.length && <Testimonials title={t('testimonials.title')} testimonials={reviewsFiltered} />}

  {!!faqsComputed.length && <CustomFaqs faqs={faqsComputed} />}

  <ContactOptions
    title={t('contactPage.title')}
    subtitle={t('contactPage.subtitle')}
    contactOptions={contactOptions}
    location={t('contactPage.location')}
  />

  <ServiceWidget
    id="services-slider"
    title={t('otherServices')}
    linkText={'👉 ' + t('viewAll')}
    linkUrl={translatePath('/services')}
    serviceToExclude={KEY}
    addScript
  />
</Layout>
