---
import EventDayLayout from '~/layouts/EventDayLayout.astro';
import { latinEvents } from '~/connectors/index';
import { getLangFromUrl, useTranslations, useTranslatedPath } from '~/i18n/utils';
import { YouMayAlsoWantLatinDay } from '~/components/dancing';

export interface Props {
  dayOfTheWeek: number;
}

const { dayOfTheWeek } = Astro.props;

// i18n setup
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);

// Data fetching
const event = latinEvents.find((e) => e.day_of_the_week === dayOfTheWeek);
// console.log("🚀 : event:", event)
if (!event) {
  throw new Error(`No event found for day ${dayOfTheWeek}`);
}
---

<EventDayLayout
  dayOfTheWeek={dayOfTheWeek}
  event={event}
  eventType="dancing"
  lang={lang}
  t={t}
  translatePath={translatePath}
  url={Astro.url}
>
  <YouMayAlsoWantLatinDay
    slot="extra-content"
    title={t('latinDancingDayPage.YouMayAlsoWant.title')}
    items={t('latinDancingDayPage.YouMayAlsoWant.items')}
  />
</EventDayLayout>
