import { properties } from '~/connectors';

/**
 * Genera i percorsi statici per le pagine delle proprietà
 * @returns Array di oggetti con i parametri per le pagine statiche
 */
export function getPropertyPaths() {
  return properties.map((p) => ({
    params: { property: String(p.name) },
  }));
}

/**
 * Trova una proprietà per name
 * @param id ID della proprietà
 * @returns La proprietà trovata o undefined
 */
export function findPropertyByName(name: string) {
  return properties.find((p) => {
    return String(p.name) === name
  });
}
