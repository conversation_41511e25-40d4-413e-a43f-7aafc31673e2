import PocketBase from 'pocketbase';
import { getImage } from 'astro:assets';

let POCKETBASE_URL = import.meta.env.POCKETBASE_URL;
const POCKETBASE_ADMIN_EMAIL = import.meta.env.POCKETBASE_ADMIN_EMAIL;
const POCKETBASE_ADMIN_PASSWORD = import.meta.env.POCKETBASE_ADMIN_PASSWORD;

// Clean up the PocketBase URL - remove admin panel paths
if (POCKETBASE_URL) {
    // Remove common admin panel paths that might be included by mistake
    POCKETBASE_URL = POCKETBASE_URL.replace(/\/_\/.*$/, '').replace(/\/admin.*$/, '');
    // Ensure it ends with a slash
    if (!POCKETBASE_URL.endsWith('/')) {
        POCKETBASE_URL += '/';
    }
}

// Debug environment variables
console.log('PocketBase Environment Variables:');
console.log('- POCKETBASE_URL (cleaned):', POCKETBASE_URL || 'Missing');
console.log('- POCKETBASE_ADMIN_EMAIL:', POCKETBASE_ADMIN_EMAIL ? 'Set' : 'Missing');
console.log('- POCKETBASE_ADMIN_PASSWORD:', POCKETBASE_ADMIN_PASSWORD ? 'Set' : 'Missing');

const TABLE_NAMES = [
    "social_agenda",
    "dancing_agenda",
    "reviews",
    "vocabulary",
    "bio",
    "services",
    "housing",
    "settings",
];

// Initialize PocketBase client
const pb = new PocketBase(POCKETBASE_URL);

// Disable auto-cancellation to prevent issues with multiple simultaneous requests
pb.autoCancellation(false);

// Cache for collection metadata to avoid repeated API calls
let collectionMetadataCache = null;

// Global authentication state to prevent multiple authentication calls
let authenticationPromise = null;

/**
 * Authenticate with PocketBase using admin credentials
 */
async function authenticateAdmin() {
    // Return existing authentication promise if one is in progress
    if (authenticationPromise) {
        console.log('🔐 Using existing authentication promise...');
        return authenticationPromise;
    }

    // Create new authentication promise
    authenticationPromise = (async () => {
        try {
            console.log('🔐 Attempting PocketBase admin authentication...');

            if (!POCKETBASE_URL || !POCKETBASE_ADMIN_EMAIL || !POCKETBASE_ADMIN_PASSWORD) {
                throw new Error('Missing required PocketBase environment variables');
            }

            if (!pb.authStore.isValid) {
                console.log('🔑 Authenticating with admin credentials...');

                // Try different authentication methods
                let authData = null;

                try {
                    // First try the deprecated but still working admins.authWithPassword
                    authData = await pb.admins.authWithPassword(POCKETBASE_ADMIN_EMAIL, POCKETBASE_ADMIN_PASSWORD);
                    console.log('✅ PocketBase admin authentication successful (using admins API)');
                } catch (adminError) {
                    console.log('⚠️ Admin API failed, trying collection-based auth...');
                    try {
                        // Try collection-based authentication for newer PocketBase versions
                        authData = await pb.collection('_superusers').authWithPassword(POCKETBASE_ADMIN_EMAIL, POCKETBASE_ADMIN_PASSWORD);
                        console.log('✅ PocketBase admin authentication successful (using collection API)');
                    } catch (collectionError) {
                        console.log('⚠️ Collection API failed, trying users collection...');
                        try {
                            // Try regular users collection as fallback
                            authData = await pb.collection('users').authWithPassword(POCKETBASE_ADMIN_EMAIL, POCKETBASE_ADMIN_PASSWORD);
                            console.log('✅ PocketBase authentication successful (using users collection)');
                        } catch (usersError) {
                            throw new Error(`All authentication methods failed. Admin: ${adminError.message}, Collection: ${collectionError.message}, Users: ${usersError.message}`);
                        }
                    }
                }

                console.log('🔍 Auth token:', authData?.token ? 'Present' : 'Missing');
                console.log('🔍 Auth record:', authData?.record ? 'Present' : 'Missing');
            } else {
                console.log('✅ Using existing valid authentication');
            }

            return true;
        } catch (error) {
            console.error('❌ PocketBase admin authentication failed:', error);
            console.error('🔍 Error details:', {
                message: error.message,
                status: error.status,
                data: error.data
            });
            // Reset the promise so it can be retried
            authenticationPromise = null;
            throw new Error(`Failed to authenticate with PocketBase: ${error.message}`);
        }
    })();

    return authenticationPromise;
}

/**
 * Fetch collection metadata from PocketBase
 */
async function fetchCollectionMetadata() {
    if (collectionMetadataCache) {
        console.log('📋 Using cached collection metadata');
        return collectionMetadataCache;
    }

    await authenticateAdmin();

    try {
        console.log('📋 Fetching collection metadata from PocketBase...');
        const collections = await pb.collections.getFullList();
        console.log(`📋 Found ${collections.length} collections in PocketBase`);

        // Debug: Log all collection names
        const collectionNames = collections.map(c => c.name);
        console.log('📋 Available collections:', collectionNames);

        // Check which required collections are missing
        const missingCollections = TABLE_NAMES.filter(name =>
            !collectionNames.some(cName => cName.toLowerCase() === name.toLowerCase())
        );

        if (missingCollections.length > 0) {
            console.warn('⚠️ Missing collections in PocketBase:', missingCollections);
        }

        collectionMetadataCache = collections;
        return collectionMetadataCache;
    } catch (error) {
        console.error('❌ Failed to fetch collection metadata:', error);
        console.error('🔍 Error details:', {
            message: error.message,
            status: error.status,
            data: error.data
        });
        throw new Error(`Failed to fetch collection metadata: ${error.message}`);
    }
}



/**
 * Get collection ID by name
 */
function getCollectionIdByName(collections, name) {
    const collection = collections.find(c => c.name?.toLowerCase() === name.toLowerCase());
    return collection?.id || null;
}







// ===== CORE FUNCTIONALITY =====

/**
 * Merge events function (same as Airtable connector)
 */
/**
 * Transform PocketBase filenames to URL objects
 */
function transformFilenames(filenames, recordId, collectionId) {
    if (!Array.isArray(filenames)) return [];

    return filenames.map(filename => {
        if (typeof filename === 'string' && filename.trim()) {
            // Generate PocketBase file URL
            const url = `${POCKETBASE_URL}api/files/${collectionId}/${recordId}/${filename}`;
            return { url };
        }
        return null;
    }).filter(Boolean);
}

/**
 * Transform single filename or array of filenames to URL objects
 */
function transformFileField(field, recordId, collectionId) {
    if (!field) return field;

    // Handle single filename string
    if (typeof field === 'string' && field.trim()) {
        const url = `${POCKETBASE_URL}api/files/${collectionId}/${recordId}/${field}`;
        return [{ url }];
    }

    // Handle array of filenames
    if (Array.isArray(field)) {
        return transformFilenames(field, recordId, collectionId);
    }

    return field;
}

/**
 * Transform event record to handle file attachments
 */
function transformEventRecord(record) {
    const recordId = record.id;
    const collectionId = record.collectionId;

    // Transform flyer fields
    const flyer = record.flyer;
    const flyerEs = record.flyer_es;

    const transformedRecord = { ...record };

    // Transform flyer field (can be string or array)
    if (flyer) {
        transformedRecord.flyer = transformFileField(flyer, recordId, collectionId);
    }

    // Transform flyer_es field (can be string or array)
    if (flyerEs) {
        transformedRecord.flyer_es = transformFileField(flyerEs, recordId, collectionId);
    }

    return transformedRecord;
}

/**
 * Transform property record to handle file attachments
 */
function transformPropertyRecord(record) {
    const recordId = record.id;
    const collectionId = record.collectionId;

    const transformedRecord = { ...record };

    // Transform cover field (single image)
    if (record.cover) {
        transformedRecord.cover = transformFileField(record.cover, recordId, collectionId);
    }

    // Transform images field (array of images)
    if (record.images) {
        transformedRecord.images = transformFileField(record.images, recordId, collectionId);
    }

    // Transform og_image field (array of images)
    if (record.og_image) {
        transformedRecord.og_image = transformFileField(record.og_image, recordId, collectionId);
    }

    return transformedRecord;
}

function mergeEvents(record) {
    const otherEvents = record.get('other_events') || [];
    const fixedEvents = record.get('fixed_events') || [];

    const recordId = record.get('id');
    const collectionId = record.get('collectionId');

    const transformedOtherEvents = transformFilenames(otherEvents, recordId, collectionId);
    const transformedFixedEvents = transformFilenames(fixedEvents, recordId, collectionId);

    return [...transformedOtherEvents, ...transformedFixedEvents];
}



/**
 * Fetch data from a specific collection with pagination
 */
async function fetchDataFromCollection(collectionName) {
    const collectionNameSanitized = collectionName.toLowerCase();
    console.log(`\n🔍 Fetching data from collection: ${collectionName}`);

    if (!TABLE_NAMES.includes(collectionNameSanitized)) {
        console.warn(`⚠️ Collection "${collectionName}" not in TABLE_NAMES list`);
        return null;
    }

    try {
        await authenticateAdmin();

        const collections = await fetchCollectionMetadata();
        const collectionId = getCollectionIdByName(collections, collectionName);

        if (!collectionId) {
            console.warn(`❌ Collection with name "${collectionName}" not found in PocketBase`);
            return { table: collectionNameSanitized, data: [] };
        }

        console.log(`✅ Found collection "${collectionName}" with ID: ${collectionId}`);

        // Fetch ALL records using pagination
        let allRecords = [];
        let page = 1;
        const perPage = 100; // Fetch 100 records per request for efficiency
        let hasMoreRecords = true;

        console.log(`📄 Starting pagination for ${collectionName}...`);

        while (hasMoreRecords) {
            try {
                console.log(`📄 Fetching page ${page} from ${collectionName}...`);
                const records = await pb.collection(collectionName).getList(page, perPage);

                console.log(`📄 Page ${page} response:`, {
                    totalItems: records.totalItems,
                    totalPages: records.totalPages,
                    currentPage: records.page,
                    itemsInPage: records.items?.length || 0
                });

                if (records && records.items && records.items.length > 0) {
                    allRecords = allRecords.concat(records.items);
                    page++;
                    hasMoreRecords = records.items.length === perPage && page <= records.totalPages;
                    console.log(`📄 Added ${records.items.length} records, total so far: ${allRecords.length}`);
                } else {
                    console.log(`📄 No more records found, stopping pagination`);
                    hasMoreRecords = false;
                }
            } catch (error) {
                console.error(`❌ Error fetching page ${page} from collection ${collectionName}:`, error);
                console.error('🔍 Error details:', {
                    message: error.message,
                    status: error.status,
                    data: error.data
                });
                hasMoreRecords = false;
            }
        }

        console.log(`✅ Fetched ${allRecords.length} total records from collection ${collectionName}`);

        // Debug: Log first record structure if available
        if (allRecords.length > 0) {
            console.log(`🔍 Sample record structure:`, Object.keys(allRecords[0]));
        }

        return { table: collectionNameSanitized, data: allRecords };
    } catch (error) {
        console.error(`❌ Error fetching data from collection ${collectionName}:`, error);
        console.error('🔍 Error details:', {
            message: error.message,
            status: error.status,
            data: error.data
        });
        return { table: collectionNameSanitized, data: [] };
    }
}

/**
 * Fetch all configured collections from PocketBase
 */
async function fetchTablesFromPocketBase() {
    return Promise.all(TABLE_NAMES.map(fetchDataFromCollection));
}

/**
 * Normalize record function (same as other connectors)
 */
const normalizeRecord = (record) => {
    // Create a simple get function for compatibility with mergeEvents
    const recordWithGet = {
        get: (field) => record[field]
    };

    const mergedEvents = mergeEvents(recordWithGet);
    const normalized = {
        id: record.id,
        ...record,
        other_events: mergedEvents  // Replace other_events with merged events
    };

    return normalized;
};

// ===== INITIALIZATION AND EXPORTS =====

// Initialize data fetching and exports
let initPromise;

if (typeof window === 'undefined') {
    // Server-side initialization
    console.log('🚀 Initializing PocketBase connector (server-side)...');
    initPromise = (async () => {
        try {


            console.log('📊 Fetching all tables from PocketBase...');
            const tables = await fetchTablesFromPocketBase();
            console.log(`📊 Fetched ${tables.length} tables:`, tables.map(t => `${t.table} (${t.data.length} records)`));

            const getTable = (tableName) => {
                const table = tables.find((t) => t.table === tableName);
                if (!table) {
                    console.warn(`⚠️ Table "${tableName}" not found in fetched tables`);
                    return [];
                }
                console.log(`📋 Getting table "${tableName}": ${table.data.length} records`);
                return table.data;
            };

            console.log('🔄 Processing services...');
            const allServices = getTable('services').map(normalizeRecord);
            console.log(`🔄 Total services before filtering: ${allServices.length}`);

            // Debug: Log first service structure
            if (allServices.length > 0) {
                console.log('🔍 Sample service structure:', {
                    id: allServices[0].id,
                    published: allServices[0].published,
                    cover: allServices[0].cover,
                    allFields: Object.keys(allServices[0])
                });
            }

            // Temporarily process all services to debug the cover field issue
            const servicesRaw = allServices; // .filter(s => s.published);
            console.log(`🔄 Processing ${servicesRaw.length} services (published filter temporarily disabled for debugging)`);

            const processedServices = await Promise.all(servicesRaw.map(async (s) => {
                try {
                    // Debug: Log the actual structure of the cover field
                    console.log('🔍 Service record structure:', {
                        id: s.id,
                        coverType: typeof s.cover,
                        coverValue: s.cover,
                        coverIsArray: Array.isArray(s.cover),
                        allFields: Object.keys(s)
                    });

                    // Handle raw PocketBase file field - cover is now a filename string or array of filenames
                    if (s.cover) {
                        let coverFilename = Array.isArray(s.cover) ? s.cover[0] : s.cover;

                        console.log('🔍 Cover filename extracted:', {
                            coverFilename,
                            type: typeof coverFilename,
                            isEmpty: !coverFilename || coverFilename.trim() === ''
                        });

                        if (coverFilename && typeof coverFilename === 'string' && coverFilename.trim() !== '') {
                            // Construct the full URL for the file
                            let coverUrl;
                            try {
                                coverUrl = pb.files.getURL(s, coverFilename);
                                console.log('🔍 Generated cover URL:', coverUrl);
                            } catch (urlError) {
                                console.warn('❌ pb.files.getURL() failed:', urlError);
                                // Fallback: manually construct the URL
                                if (POCKETBASE_URL && s.collectionId && s.id) {
                                    coverUrl = `${POCKETBASE_URL}api/files/${s.collectionId}/${s.id}/${coverFilename}`;
                                    console.log('🔍 Fallback cover URL:', coverUrl);
                                } else {
                                    console.warn('❌ Cannot construct fallback URL - missing required data:', {
                                        POCKETBASE_URL: !!POCKETBASE_URL,
                                        collectionId: s.collectionId,
                                        recordId: s.id
                                    });
                                    return s;
                                }
                            }

                            if (coverUrl) {
                                console.log('✅ Processing image with URL:', coverUrl);
                                return {
                                    ...s,
                                    cover: await getImage({ src: coverUrl, format: 'webp', width: 1080, height: 1080 })
                                };
                            }
                        }
                    } else {
                        console.log('⚠️ No cover field found in service record');
                    }
                    return s;
                } catch (error) {
                    console.warn('❌ Error processing service image:', error);
                    console.warn('🔍 Service record that caused error:', {
                        id: s?.id,
                        cover: s?.cover,
                        collectionId: s?.collectionId
                    });
                    return s;
                }
            }));

            const result = {
                latinEvents: getTable('dancing_agenda').map(record => transformEventRecord(normalizeRecord(record))),
                socialEvents: getTable('social_agenda').map(record => transformEventRecord(normalizeRecord(record))),
                reviews: getTable('reviews').map(normalizeRecord),
                vocabulary: getTable('vocabulary').map(normalizeRecord),
                bio: getTable('bio').map(normalizeRecord),
                settings: getTable('settings').map(normalizeRecord),
                properties: getTable('housing').map(record => transformPropertyRecord(normalizeRecord(record))),
                services: processedServices
            };

            console.log('✅ PocketBase connector initialization complete!');
            console.log('📊 Final data summary:', {
                latinEvents: result.latinEvents.length,
                socialEvents: result.socialEvents.length,
                reviews: result.reviews.length,
                vocabulary: result.vocabulary.length,
                bio: result.bio.length,
                settings: result.settings.length,
                properties: result.properties.length,
                services: result.services.length
            });

            return result;
        } catch (error) {
            console.error('❌ Error initializing PocketBase connector:', error);
            console.error('🔍 Error details:', {
                message: error.message,
                stack: error.stack
            });
            // Return empty data structures to maintain compatibility
            return {
                latinEvents: [],
                socialEvents: [],
                reviews: [],
                vocabulary: [],
                bio: [],
                settings: [],
                properties: [],
                services: []
            };
        }
    })();
} else {
    // Client-side - return empty data
    console.log('🚀 Initializing PocketBase connector (client-side) - returning empty data');
    initPromise = Promise.resolve({
        latinEvents: [],
        socialEvents: [],
        reviews: [],
        vocabulary: [],
        bio: [],
        settings: [],
        properties: [],
        services: []
    });
}

// Export the data (same interface as other connectors)
const data = await initPromise;

export const latinEvents = data.latinEvents;
export const socialEvents = data.socialEvents;
export const reviews = data.reviews;
export const vocabulary = data.vocabulary;
export const bio = data.bio;
export const settings = data.settings;
export const properties = data.properties;
export const services = data.services;

// Export the fetch function for compatibility
export { fetchTablesFromPocketBase };
