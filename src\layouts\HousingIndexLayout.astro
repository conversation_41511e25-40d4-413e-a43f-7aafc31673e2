---
import Layout from '~/layouts/PageLayout.astro';
import CallToAction from '~/components/widgets/CallToAction.astro';
import { Icon } from 'astro-icon/components';
import { getAsset } from '~/utils/permalinks';
import PropertiesShowcase from '~/components/real-estate/PropertiesShowcase.astro';
import { settings, properties } from '~/connectors/index';
import { getLangFromUrl, useTranslations, useTranslatedPath } from '~/i18n/utils';
import { getImage } from 'astro:assets';
import housingBackground from '/src/assets/images/heroes/housing.webp';
import FollowSectionSocials from '~/components/common/FollowSectionSocials.astro';

// Utils & Constants
const HOUSING_ENABLED_KEY = 'housing.isEnabled';

// Page Setup
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);

const hasProperties = !!properties.length;
const housingIsEnabled = settings.find((s) => s.key === HOUSING_ENABLED_KEY)?.value;
const optimizedBackground = await getImage({ src: housingBackground, format: 'webp' });

const metadata = {
  title: 'Gran Canaria SBK Dancing Group',
};

const socialLinks = [
  { ariaLabel: 'Facebook', icon: 'tabler:brand-facebook', href: 'https://www.facebook.com/livegrancanaria' },
  {
    ariaLabel: 'Instagram',
    icon: 'tabler:brand-instagram',
    href: 'https://www.instagram.com/livegrancanaria_/',
  },
  { ariaLabel: 'Whatsapp', icon: 'tabler:brand-whatsapp', href: 'https://www.instagram.com/grancanariadancinggroup/' },
  //   { ariaLabel: 'RSS', icon: 'tabler:rss', href: getAsset('/rss.xml') },
];

// Section Configs
const sectionsConfig = {
  properties: {
    id: 'properties-section',
    // title: 'real-estate',
    title: t('real-estate'),
    information: 'Hand-Picked & Verified Offers',
    linkText: '🏡 Rent or Buy',
    linkUrl: translatePath('/real-estate'),
  },
};
---

<Layout metadata={metadata} noDonate>
  <!-- Hero Widget ******************* -->
  <div
    class="w-full dark:bg-gray-800"
    style={`background-image: url(${optimizedBackground.src}); background-position: center center; background-blend-mode: multiply; background-size: cover;`}
  >
    <div class="container flex flex-col flex-wrap content-center justify-center p-4 py-20 mx-auto md:p-10">
      <p class="text-center text-base text-secondary dark:text-blue-200 font-bold tracking-wide uppercase">
        Helping you with accomodation
      </p>

      <h1 class="text-5xl antialiased font-semibold leadi text-center dark:text-gray-100">You'll also feel at home</h1>
      <p class="pt-2 pb-4 text-xl antialiased text-center dark:text-gray-100">Take a look to our approved solutions</p>

      <ul class="flex justify-center mb-4 md:order-1 -ml-2 md:ml-4 md:mb-0 rtl:ml-0 rtl:-mr-2 rtl:md:ml-0 rtl:md:mr-4">
        {
          socialLinks.map(({ ariaLabel, href, text, icon }) => (
            <li>
              <a
                class="text-muted dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5 inline-flex items-center"
                target="_blank"
                aria-label={ariaLabel}
                href={href}
              >
                {icon && <Icon name={icon} class="w-5 h-5" />}
                <Fragment set:html={text} />
              </a>
            </li>
          ))
        }
      </ul>
    </div>
  </div>

  {
    hasProperties && housingIsEnabled && (
      <PropertiesShowcase properties={properties} {...sectionsConfig.properties} addScript>
        <div class="absolute inset-0 bg-blue-50 dark:bg-transparent" slot="bg" />
      </PropertiesShowcase>
    )
  }

  <!-- Join us Widget *********** -->
  <FollowSectionSocials section="livegc" ctaText={t('followUs')} />

  <!-- Stats Widget ****************** -->

  <!-- <Stats
    stats={[
      { title: 'years of experience', amount: '20' },
      { title: 'Events organized in GC', amount: '100+' },
      { title: 'Followers', amount: '2.4k' },
    ]}
  /> -->

  <!-- Testimonials Widget *********** -->

  <!-- <Testimonials
    title="Words from real people"
    testimonials={[
      {
        testimonial: `The designs are not only visually appealing but also highly professional. The templates have saved me a significant amount of time while helping me make a lasting impression on my clients.`,
        name: 'Emily Kennedy',
        job: 'Front-end developer',
        image: {
          src: 'https://images.unsplash.com/photo-1618835962148-cf177563c6c0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=930&q=80',
          alt: 'Emily Kennedy Image',
        },
      },
      {
        testimonial: `It beautifully showcases my work, with its clean and elegant design that lets my photographs shine. Customization was a breeze, even for a non-tech person like me. The result is a professional and immersive portfolio that's garnered numerous compliments.`,
        name: 'Sarah Hansen',
        job: 'Photographer',
        image: {
          src: 'https://images.unsplash.com/photo-1561406636-b80293969660?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80',
          alt: 'Sarah Hansen Image',
        },
      },
      {
        testimonial: `I discovered these templates and I'm impressed by their variety and quality. They've helped me establish a consistent brand image across my marketing and social platforms, elevating my business's overall appearance.`,
        name: 'Mark Wilkinson',
        job: 'Small business owner',
        image: {
          src: 'https://images.unsplash.com/photo-1545167622-3a6ac756afa4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=824&q=80',
          alt: 'Mark Wilkinson Image',
        },
      },
    ]}  
  /> -->

  <!-- CallToAction Widget *********** -->
  <CallToAction
    title="Let's connect"
    subtitle="Do you want to learn more, propose a collaboration, join one of our events, or discuss anything else with us? Feel free to get in touch. We're here for you!"
    callToAction={{
      text: 'Contact us',
      href: '/contact',
    }}
  />
</Layout>
