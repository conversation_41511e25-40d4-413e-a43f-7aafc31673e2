import { getPermalink, getHomePermalink, getAsset } from './utils/permalinks';
import { settings } from '~/connectors';

const isEnabled = (key) => settings.find((s) => s.key === key)?.value;

export const headerData = {
  links: [
    {
      text: 'Home',
      href: getHomePermalink() ? `/${getHomePermalink()}` : '/',
    },
    {
      text: 'About',
      href: '/about',
    },
    {
      text: 'Sections',
      links: [
        ...(isEnabled('services.isEnabled') ? [{ text: 'services', href: getPermalink('/services') }] : []),
        ...(isEnabled('housing.isEnabled') ? [{ text: 'housing', href: getPermalink('/real-estate') }] : []),
        { text: 'dancing', href: getPermalink('/dancing') },
        { text: 'socialization', href: getPermalink('/socialization') },
      ],
    },
    {
      text: 'Contact',
      href: '/contact',
    },
  ],
};

export const footerData = {
  links: [
    {
      title: 'Sections',
      links: [
        ...(isEnabled('services.isEnabled') ? [{ text: 'services', href: '/services' }] : []),
        { text: 'articles', href: '/articles' },
        ...(isEnabled('housing.isEnabled') ? [{ text: 'housing', href: '/housing' }] : []),
        { text: 'dancing', href: '/dancing' },
        { text: 'socialization', href: '/socialization' },
      ],
    },
    {
      title: 'Platform',
      links: [
        { text: 'About', href: 'about' },
      ],
    },
    {
      title: 'Support',
      links: [
        { text: 'Contact', href: 'contact' },
      ],
    },
  ],
  secondaryLinks: [
    { text: 'Privacy', href: getPermalink('/privacy') },
    { text: 'Terms', href: getPermalink('/terms') },
  ],
  socialLinks: [
    { ariaLabel: 'Whatsapp', icon: 'tabler:brand-whatsapp', href: 'https://chat.whatsapp.com/FJ6fld7XUmKFfFXec5W9jY' },
    { ariaLabel: 'Instagram', icon: 'tabler:brand-instagram', href: 'https://www.instagram.com/livegrancanaria_/' },
    { ariaLabel: 'Facebook', icon: 'tabler:brand-facebook', href: 'https://www.facebook.com/livegrancanaria' },
    { ariaLabel: 'Twitter', icon: 'tabler:brand-twitter', href: 'https://twitter.com/livegrancanaria' },
  ],
  footNote: `<a class="text-blue-600 hover:underline dark:text-gray-200" href="https://livegrancanaria.com/">LiveGranCanaria.com</a>`,
};
