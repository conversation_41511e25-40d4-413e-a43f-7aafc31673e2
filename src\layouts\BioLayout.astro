---
import Layout from '~/layouts/Layout.astro';
import logo from '~/assets/images/ui/logo.svg';
import { Image } from 'astro:assets';
import { bio } from '~/connectors/index';
import { socialLinks } from '~/data/socialLinks';
import { Icon } from 'astro-icon/components';
import { getLangFromUrl, useTranslations, useTranslatedPath } from '~/i18n/utils';
import { defaultLang } from '~/i18n/ui';
import LanguagePicker from '~/components/common/LanguagePicker.astro';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);

const section = 'livegc';

const platformSocialLinks = socialLinks.filter((link) => link.sections.includes(section));

const metadata = {
  title: 'Bio',
  description: t('bio.metadata.description'),
};
---

<Layout metadata={metadata}>
  <section>
    <div class="container px-6 mx-auto">
      <!--  Header -->
      <div class="flex flex-col items-center p-8 group rounded-xl">
        <a href={`${lang !== defaultLang ? '/' + lang : '/'}`}>
          <Image
            class="object-cover mx-2 rounded-full w-32 h-32 ring-2 ring-gray-300 dark:ring-gray-500"
            src={logo}
            alt="Logo LiveGranCanaria"
            loading="eager"
            width={160}
            height={160}
            loading="eager"
            decoding="async"
          />
        </a>

        <div>
          <h1 class="mt-4 text-2xl font-semibold text-gray-700 capitalize dark:text-white group-hover:text-white">
            LiveGranCanaria.com
          </h1>
          <p class="mt-2 text-gray-500 capitalize dark:text-gray-300 group-hover:text-gray-300">
            {t('bio.intro')}
          </p>

          <!-- Buttons -->
          <div class="flex flex-col w-full mx-auto my-4">
            {
              bio.map((item) => {
                return (
                  <div class="btn flex justify-center mx-auto w-full font-medium my-2">
                    <div class="w-full">
                      <a href={item.url_en} rel="noopener noreferrer" class="flex items-start capitalize">
                        <p class="mr-3 h-6 w-6">{item.emoji}</p>
                        {t(item.label_key)}
                        <div class="ml-auto mt-0.5 pl-4">
                          <svg
                            class="h-5 w-5 text-gray-400"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z"
                            />
                          </svg>
                        </div>
                      </a>
                    </div>
                  </div>
                );
              })
            }
            <!-- End Buttons -->
          </div>
        </div>
        <!-- End Header -->

        <!-- Social Buttons -->
        <div class="hidden">
          <div class="bg-green-500"></div>
          <div class="bg-red-500"></div>
          <div class="bg-blue-500"></div>
        </div>

        <div class="flex flex-wrap justify-center gap-2">
          {
            platformSocialLinks.map((social) => {
              return (
                <a
                  href={social.href}
                  target="_blank"
                  class={`bg-${social.color}-500 hover:scale-150 hover:ease-in duration-300 hover:animate-pulse p-2 font-semibold text-white inline-flex items-center space-x-2 rounded mx-2`}
                >
                  <Icon name={social.icon} class="w-6 h-6" />
                </a>
              );
            })
          }
        </div>
        <!-- End Social Buttons -->

        <!-- Language Switcher -->
        <div class="text-center my-4"><LanguagePicker /></div>
        <!-- End Language Switcher -->
      </div>
    </div>
  </section>
</Layout>
