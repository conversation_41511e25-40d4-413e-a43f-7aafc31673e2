---
import WidgetWrapper from '~/components/ui/WidgetWrapper.astro';
import Button from '~/components/ui/Button.astro';
import type { Widget } from '~/types';
import { latinEvents, socialEvents } from '~/connectors/index';
import AgendaSection from './AgendaSection.astro';
import { getLangFromUrl, useTranslations, useTranslatedPath } from '~/i18n/utils';

interface Props extends Widget {
  title?: string;
  linkText?: string;
  linkUrl?: string | URL;
  information?: string;
}

const {
  id,
  isDark = false,
  classes = {},
  bg = await Astro.slots.render('bg'),
  title = await Astro.slots.render('title'),
  linkText,
  linkUrl,
  information = await Astro.slots.render('information'),
} = Astro.props;

const lang = getLangFromUrl(Astro.url);
const translatePath = useTranslatedPath(lang);
const t = useTranslations(lang);

const agendas = [
  {
    title: t('indexPage.latestLatinDanceEvents.title'),
    information: t('indexPage.latestLatinDanceEvents.information'),
    linkText: t('indexPage.latestLatinDanceEvents.linkText'),
    linkUrl: translatePath('/dancing'),

    events: latinEvents,
    pathPrefix: 'dancing/agenda',
    linkSection: translatePath('/dancing'),
  },
  {
    title: t('indexPage.latestSocialEvents.title'),
    information: t('indexPage.latestSocialEvents.information'),
    linkText: t('indexPage.latestSocialEvents.linkText'),
    linkUrl: translatePath('/socialization'),
    events: socialEvents,
    pathPrefix: 'socialization/agenda',
    linkSection: translatePath('/socialization'),
  },
];
---

<WidgetWrapper id={id} isDark={isDark} containerClass={classes?.container} bg={bg}>
  <header class="flex flex-col lg:justify-between lg:flex-row mb-8">
    {
      title && (
        <div class="md:max-w-sm">
          <h2
            class="text-3xl font-bold tracking-tight sm:text-4xl sm:leading-none group font-heading mb-2"
            set:html={title}
          />
          {linkText && linkUrl && (
            <Button variant="link" href={linkUrl}>
              {linkText}
            </Button>
          )}
        </div>
      )
    }
    {information && <p class="text-muted dark:text-slate-400 lg:text-sm lg:max-w-md" set:html={information} />}
  </header>

  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    {
      agendas.map((agenda, index) => (
        <AgendaSection
          {...agenda}
          bgClass={index % 2 === 0 ? 'bg-gray-200 dark:bg-gray-900' : 'bg-gray-300 dark:bg-gray-800'}
        />
      ))
    }
  </div>
</WidgetWrapper>
