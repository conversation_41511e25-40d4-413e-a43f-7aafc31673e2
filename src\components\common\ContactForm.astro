---
import { Icon } from 'astro-icon/components';
import { getLangFromUrl, useTranslations, useTranslatedPath } from '~/i18n/utils';

// i18n setup
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);

// Get the current URL for sharing
const url = Astro.url.toString();

// Default property title (will be overridden by props if provided)
const defaultTitle = t('contact_us') || 'Contact Us';

interface Props {
  title?: string;
  address?: string;
}

const { title = defaultTitle, address = '' } = Astro.props;
---

<div class="lg:col-span-1">
  <div class="sticky top-8">
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
      <div class="mb-4 p-2 text-center bg-gray-50 dark:bg-gray-900 rounded-lg">
        <span class="block text-2xl font-bold text-gray-900 dark:text-white">{t('contact_form')}</span>
        <span class="block text-sm text-gray-600 dark:text-gray-400 italic">{t('fill_out_fields')}</span>
      </div>

      <!-- Booking Form -->
      <form id="contact-form" class="space-y-4">
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{t('first_name')}</label>
            <input
              id="first-name"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{t('last_name')}</label>
            <input
              id="last-name"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{t('phone_number')}</label>
          <input
            id="phone"
            type="tel"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{t('email')}</label>
          <input
            id="email"
            type="email"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{t('message')}</label>
          <textarea
            id="message"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            rows="4"></textarea>
        </div>

        <div class="space-y-3">
          <button
            type="submit"
            class="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-4 rounded-lg transition duration-150"
          >
            {t('submit')}
          </button>

          <!-- Additional contact options -->
          <div class="grid grid-cols-2 gap-3 mt-4">
            <a
              id="whatsapp-link"
              href="#"
              target="_blank"
              rel="noopener noreferrer"
              class="flex items-center justify-center bg-green-600 hover:bg-green-700 text-white py-2 px-3 rounded-lg transition-colors"
            >
              <Icon name="tabler:brand-whatsapp" class="w-5 h-5 mr-2" />
              <span>{t('whatsapp')}</span>
            </a>

            <a
              id="email-link"
              href="#"
              class="flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded-lg transition-colors"
            >
              <Icon name="tabler:mail" class="w-5 h-5 mr-2" />
              <span>{t('email')}</span>
            </a>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Toast Notification (Re-used from PropertySidebar) -->
<div
  id="contact-toast-notification"
  class="fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-4 py-2 rounded-lg shadow-lg hidden flex items-center transition-opacity duration-300 opacity-0 z-50"
>
  <Icon name="tabler:check" class="w-5 h-5 mr-2 text-green-400" />
  <span id="contact-toast-message"></span>
</div>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const form = document.getElementById('contact-form');
    const firstNameInput = document.getElementById('first-name');
    const lastNameInput = document.getElementById('last-name');
    const phoneInput = document.getElementById('phone');
    const emailInput = document.getElementById('email');
    const messageInput = document.getElementById('message');
    const whatsappLink = document.getElementById('whatsapp-link');
    const emailLink = document.getElementById('email-link');
    const toastNotification = document.getElementById('contact-toast-notification');
    const toastMessage = document.getElementById('contact-toast-message');

    // Get property title and address from props or use defaults
    const title = document.querySelector('h1')?.textContent || '{Astro.props.title}';
    const address = '{Astro.props.address}';
    const url = window.location.href;

    // Update WhatsApp link dynamically
    function updateWhatsappLink() {
      const firstName = firstNameInput.value || '';
      const lastName = lastNameInput.value || '';
      const phone = phoneInput.value || '';
      const email = emailInput.value || '';
      const message = messageInput.value || '';

      const fullName = `${firstName} ${lastName}`.trim();
      const contactInfo = `${phone} / ${email}`.trim();

      let whatsappText = `${title}`;
      if (address) whatsappText += ` - ${address}`;
      if (fullName) whatsappText += `/n${fullName}`;
      if (contactInfo) whatsappText += `/n${contactInfo}`;
      if (message) whatsappText += `/n/n${message}`;

      whatsappLink.href = `https://wa.me/1234567890?text=${encodeURIComponent(whatsappText)}`;
    }

    // Update Email link dynamically
    function updateEmailLink() {
      const firstName = firstNameInput.value || '';
      const lastName = lastNameInput.value || '';
      const phone = phoneInput.value || '';
      const email = emailInput.value || '';
      const message = messageInput.value || '';

      const fullName = `${firstName} ${lastName}`.trim();
      const contactInfo = `${phone} / ${email}`.trim();

      let emailSubject = `${title}`;
      if (address) emailSubject += ` - ${address}`;

      let emailBody = '';
      if (fullName) emailBody += `Name: ${fullName}/n`;
      if (phone) emailBody += `Phone: ${phone}/n`;
      if (email) emailBody += `Email: ${email}/n/n`;
      if (message) emailBody += `Message:/n${message}/n/n`;
      emailBody += `Sent from: ${url}`;

      emailLink.href = `mailto:<EMAIL>?subject=${encodeURIComponent(emailSubject)}&body=${encodeURIComponent(emailBody)}`;
    }

    // Show toast notification (re-used from PropertySidebar)
    function showToast(message) {
      toastMessage.textContent = message;
      toastNotification.classList.remove('hidden');

      // Fade in
      setTimeout(() => {
        toastNotification.classList.remove('opacity-0');
        toastNotification.classList.add('opacity-100');
      }, 10);

      // Fade out after delay
      setTimeout(() => {
        toastNotification.classList.remove('opacity-100');
        toastNotification.classList.add('opacity-0');

        setTimeout(() => {
          toastNotification.classList.add('hidden');
        }, 300);
      }, 3000);
    }

    // Update links when form inputs change
    [firstNameInput, lastNameInput, phoneInput, emailInput, messageInput].forEach((input) => {
      input.addEventListener('input', () => {
        updateWhatsappLink();
        updateEmailLink();
      });
    });

    // Initialize links
    updateWhatsappLink();
    updateEmailLink();

    // Handle form submission
    form.addEventListener('submit', (e) => {
      e.preventDefault();

      // Here you would typically send the form data to a server
      // For now, we'll just show a toast notification
      showToast('Form submitted successfully!');

      // You could also clear the form after submission
      // form.reset();
    });

    // Handle WhatsApp click
    whatsappLink.addEventListener('click', (e) => {
      // Since we're opening in a new tab, we don't need to prevent default
      showToast('Opening WhatsApp...');
    });

    // Handle Email click
    emailLink.addEventListener('click', (e) => {
      // Since we're opening the email client, we don't need to prevent default
      showToast('Opening email client...');
    });
  });
</script>

<style>
  /* Animations */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
</style>
